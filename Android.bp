java_defaults {
	name: "HeadUnit_deafualt",
	use_embedded_native_libs: true,
	srcs: ["src/com/google/android/projection/sink/ui/MainActivity.java"],
	jni_libs: ["libgalreceiver_jni",]
}
android_library {
	name:"HeadUnitPluginLib",
	srcs: [],
	installable: true,
	resource_dirs:["res"],
	sdk_version: "current",
	min_sdk_version: "26",
	dex_preopt:{
		enabled: true
	},
	static_libs: [
		"liblite",
		"libcommon_proto",
		"libprotocol_proto",
		"libwifi_discovery_proto",
		"libwifi_projection_protocol_proto",
		"libandroid_protocol_lib_java_only",
		],
	
}
android_app {
    name: "HeadUnit",
    defaults: ["HeadUnit_deafualt"],
    srcs: ["src/**/*.java"],
    resource_dirs: ["res"],
    manifest: "AndroidManifest.xml",
    certificate: "platform",
    privileged: true,
    product_specific: true,
	sdk_version: "system_current",
	min_sdk_version: "26",
    static_libs: [
    	"HeadUnitPluginLib",
        "androidx.localbroadcastmanager_localbroadcastmanager",
        "androidx.appcompat_appcompat",    
    ],
    
}

<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android" >

    <!--
         NOTE: Hide buttons to simplify the UI. Users can touch outside the dialog to
         dismiss it.
    -->
    <!-- NOTE: ListPreference's summary should be set to its value by the activity code. -->
    <ListPreference
        android:defaultValue="4"
        android:entries="@array/pref_preset_list_titles"
        android:entryValues="@array/pref_preset_list_values"
        android:key="preset_list"
        android:negativeButtonText="@null"
        android:positiveButtonText="@null"
        android:title="@string/pref_title_preset_screens" />

    <CheckBoxPreference
        android:defaultValue="true"
        android:key="scale_checkbox"
        android:summary="@string/pref_scale_screen"
        android:title="@string/pref_title_scale_screen" />

    <CheckBoxPreference
        android:defaultValue="false"
        android:key="debug_mode"
        android:summary="@string/pref_debug_mode"
        android:title="@string/pref_title_debug_mode" />

    <EditTextPreference
        android:key="screen_width"
        android:title="@string/pref_screen_width"
        android:maxLines="1"
        android:singleLine="true"
        android:selectAllOnFocus="true"
        android:enabled="false"
        android:inputType="number"
        />
    <EditTextPreference
        android:key="screen_height"
        android:title="@string/pref_screen_height"
        android:maxLines="1"
        android:singleLine="true"
        android:selectAllOnFocus="true"
        android:enabled="false"
        android:inputType="number"
        />
    <EditTextPreference
        android:key="screen_dpi"
        android:title="@string/pref_screen_dpi"
        android:maxLines="1"
        android:singleLine="true"
        android:selectAllOnFocus="true"
        android:enabled="false"
        android:inputType="number"
        />

    <ListPreference
        android:defaultValue="@string/pref_location_source_gps"
        android:entries="@array/pref_location_source_list_titles"
        android:entryValues="@array/pref_location_source_list_values"
        android:key="location_source_list"
        android:negativeButtonText="@null"
        android:positiveButtonText="@null"
        android:title="@string/pref_title_location_source" />

    <CheckBoxPreference
        android:defaultValue="false"
        android:key="framerate_restrict_to_30"
        android:summary="@string/pref_framerate_restrict"
        android:title="@string/pref_title_framerate_restrict" />

    <EditTextPreference
        android:key="listen_tcp_port"
        android:title="@string/pref_listen_tcp_port"
        android:maxLines="1"
        android:singleLine="true"
        android:selectAllOnFocus="true"
        android:inputType="number"
        android:defaultValue="0" />

    <EditTextPreference
        android:key="car_wifi_ssid"
        android:title="@string/pref_car_wifi_ssid"
        android:maxLines="1"
        android:singleLine="true"
        android:selectAllOnFocus="true"
        android:inputType="textNoSuggestions"
        android:defaultValue="aawp" />

    <EditTextPreference
        android:key="car_wifi_password"
        android:title="@string/pref_car_wifi_password"
        android:maxLines="1"
        android:singleLine="true"
        android:selectAllOnFocus="true"
        android:inputType="textNoSuggestions"
        android:defaultValue="wifipassword" />

    <ListPreference
        android:defaultValue="4"
        android:entries="@array/pref_wifi_security_list_titles"
        android:entryValues="@array/pref_wifi_security_list_values"
        android:key="wifi_security_list"
        android:negativeButtonText="@null"
        android:positiveButtonText="@null"
        android:title="@string/pref_title_wifi_security" />

    <CheckBoxPreference
        android:defaultValue="true"
        android:key="wifi_auto_start"
        android:title="@string/pref_wifi_auto_start" />

    <!-- SKIP_THIS_BLUETOOTH is a magic fake Bluetooth MAC address that skips pairing. -->
    <EditTextPreference
        android:key="car_bluetooth_address"
        android:title="@string/pref_car_bluetooth_address"
        android:maxLines="1"
        android:singleLine="true"
        android:selectAllOnFocus="true"
        android:inputType="textCapCharacters|textNoSuggestions"
        android:defaultValue="SKIP_THIS_BLUETOOTH" />

    <CheckBoxPreference
        android:defaultValue="true"
        android:key="car_supports_bluetooth_numeric_comparison"
        android:title="@string/pref_car_supports_bluetooth_numeric_comparison" />

    <CheckBoxPreference
        android:defaultValue="true"
        android:key="car_supports_bluetooth_pin"
        android:title="@string/pref_car_supports_bluetooth_pin" />

    <CheckBoxPreference
        android:defaultValue="false"
        android:key="audio_focus_use_restricted_mode"
        android:title="@string/pref_audio_focus_use_restricted_mode" />

    <CheckBoxPreference
        android:defaultValue="false"
        android:key="rotary_controller_enabled"
        android:title="@string/pref_rotary_controller_enabled" />
    <CheckBoxPreference
        android:defaultValue="true"
        android:key="touch_screen_enabled"
        android:title="@string/pref_touch_screen_enabled" />
    <CheckBoxPreference
        android:defaultValue="true"
        android:key="touchpad_navigation_enabled"
        android:title="@string/pref_touchpad_navigation_enabled" />

    <CheckBoxPreference
        android:defaultValue="false"
        android:key="generate_missing_sensors"
        android:title="@string/pref_generate_missing_sensors" />

    <CheckBoxPreference
        android:defaultValue="false"
        android:key="use_tablet_compass_enabled"
        android:title="@string/pref_use_tablet_compass_enabled" />

    <CheckBoxPreference
        android:defaultValue="false"
        android:key="use_obd2_enabled"
        android:title="@string/pref_use_obd2_enabled" />

    <ListPreference
        android:defaultValue=""
        android:key="obd2_device"
        android:title="@string/pref_obd2_device"
        android:dependency="use_obd2_enabled" />

    <CheckBoxPreference
        android:defaultValue=""
        android:key="auto_start_local_mode"
        android:title="@string/auto_start_local_mode" />

    <CheckBoxPreference
        android:defaultValue="false"
        android:key="use_sun_calculator"
        android:title="@string/pref_use_sun_calculator" />

    <CheckBoxPreference
        android:defaultValue="true"
        android:key="use_aac_for_media_stream"
        android:title="@string/pref_use_aac_for_media_stream_enabled" />

    <CheckBoxPreference
        android:defaultValue="false"
        android:key="use_aac_for_tts_stream"
        android:title="@string/pref_use_aac_for_tts_stream_enabled" />

    <CheckBoxPreference
        android:defaultValue="true"
        android:key="auto_start_projection"
        android:title="@string/pref_auto_start_projection" />

    <CheckBoxPreference
        android:defaultValue="false"
        android:key="volume_toggle_debug"
        android:title="@string/pref_volume_toggle_debug" />

    <CheckBoxPreference
      android:defaultValue="false"
      android:key="show_vf_dialog"
      android:title="@string/pref_show_vf_dialog" />
</PreferenceScreen>

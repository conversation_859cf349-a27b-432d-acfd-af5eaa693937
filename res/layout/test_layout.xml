<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/tools"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:layout_alignParentRight="true"
    android:background="@color/background_debug_controls"
    android:orientation="vertical"
    android:visibility="invisible">
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content">
        <GridLayout
            android:id="@+id/sim_dpad"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" >
            <Button
                android:id="@+id/rotary_ccw"
                android:layout_row="0"
                android:layout_column="0"
                android:text="@string/rotary_ccw_label" />
            <Button
                android:id="@+id/dpad_up"
                android:layout_row="0"
                android:layout_column="1"
                android:text="@string/dpad_up_label" />
            <Button
                android:id="@+id/rotary_cw"
                android:layout_row="0"
                android:layout_column="2"
                android:text="@string/rotary_cw_label" />
            <Button
                android:id="@+id/dpad_left"
                android:layout_row="1"
                android:layout_column="0"
                android:text="@string/dpad_left_label" />
            <Button
                android:id="@+id/dpad_center"
                android:layout_row="1"
                android:layout_column="1"
                android:text="@string/dpad_center_label" />
            <Button
                android:id="@+id/dpad_right"
                android:layout_row="1"
                android:layout_column="2"
                android:text="@string/dpad_right_label" />
            <Button
                android:id="@+id/rotary_ccw_fling"
                android:layout_row="2"
                android:layout_column="0"
                android:text="@string/rotary_ccw_fling_label" />
            <Button
                android:id="@+id/dpad_down"
                android:layout_row="2"
                android:layout_column="1"
                android:text="@string/dpad_down_label" />
            <Button
                android:id="@+id/rotary_cw_fling"
                android:layout_row="2"
                android:layout_column="2"
                android:text="@string/rotary_cw_fling_label" />
        </GridLayout>
        <View
            android:id="@+id/touchpad"
            android:layout_width="256dip"
            android:layout_height="256dip"
            android:background="#99ffffff" />
    </LinearLayout>
    <ScrollView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical" >
            <TextView
                android:id="@+id/fps"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content" />
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal" >
                <Button
                    android:id="@+id/native_transient_audio"
                    android:text="@string/label_native_transient"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <ToggleButton
                    android:id="@+id/audio_focus"
                    android:checked="true"
                    android:textOn="AF On"
                    android:textOff="AF Off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <ToggleButton
                    android:id="@+id/video_focus"
                    android:checked="false"
                    android:textOn="VF On"
                    android:textOff="VF Off"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <ToggleButton
                    android:id="@+id/nav_focus"
                    android:checked="true"
                    android:textOn="NavFcs Phone"
                    android:textOff="NavFcs Car"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <Button
                    android:id="@+id/byebye_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/byebye" />
                <Button
                    android:id="@+id/disconnect_gvve_button"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="@string/disconnect_gvve" />
            </LinearLayout>
            <LinearLayout
                android:id="@+id/simulated_location_controls"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_alignParentRight="true"
                android:visibility="gone" >
                <LinearLayout
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal" >
                    <Button
                        android:id="@+id/simulated_location_faster"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="@string/simulated_location_faster" />
                    <TextView
                        android:id="@+id/simulated_location_current_speed"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:paddingTop="@dimen/speed_controls_speed_label_padding"
                        android:paddingBottom="@dimen/speed_controls_speed_label_padding" />
                    <Button
                        android:id="@+id/simulated_location_slower"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="@string/simulated_location_slower" />
                    <Button
                        android:id="@+id/simulated_location_reset"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:layout_gravity="center_horizontal"
                        android:text="@string/simulated_location_reset" />
                </LinearLayout>
            </LinearLayout>
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal" >
                <Button
                    android:id="@+id/bvra_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/bvra" />
                <Button
                    android:id="@+id/nav_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/nav" />
                <Button
                    android:id="@+id/media_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/media" />
                <Button
                    android:id="@+id/tel_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/tel" />
                <Button
                    android:id="@+id/back_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/back" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal" >
                <ToggleButton
                    android:id="@+id/night_mode_auto"
                    android:checked="true"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textOn="@string/nm_auto"
                    android:textOff="@string/nm_auto" />
                <ToggleButton
                    android:id="@+id/night_mode_day"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textOn="@string/nm_day"
                    android:textOff="@string/nm_day" />
                <ToggleButton
                    android:id="@+id/night_mode_night"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textOn="@string/nm_night"
                    android:textOff="@string/nm_night" />
            </LinearLayout>
            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal" >
                <Button
                    android:id="@+id/call"
                    android:text="@string/call"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <Button
                    android:id="@+id/end_call"
                    android:text="@string/end_call"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <Button
                    android:id="@+id/media_prev"
                    android:text="@string/media_prev"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <Button
                    android:id="@+id/media_next"
                    android:text="@string/media_next"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <Button
                    android:id="@+id/media_play"
                    android:text="@string/media_play"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <Button
                    android:id="@+id/media_pause"
                    android:text="@string/media_pause"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <Button
                    android:id="@+id/media_play_pause"
                    android:text="@string/media_play_pause"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal" >
                <ToggleButton
                    android:id="@+id/keyboard_lockout_auto"
                    android:checked="true"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textOff="@string/keyboard_lockout_auto"
                    android:textOn="@string/keyboard_lockout_auto" />
                <ToggleButton
                    android:id="@+id/keyboard_lockout_force"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textOff="@string/keyboard_lockout_force"
                    android:textOn="@string/keyboard_lockout_force" />
                <ToggleButton
                    android:id="@+id/keyboard_lockout_override"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textOff="@string/keyboard_lockout_override"
                    android:textOn="@string/keyboard_lockout_override" />
            </LinearLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="horizontal" >
                <ToggleButton
                    android:id="@+id/bluetooth_already_paired_toggle"
                    android:checked="false"
                    android:textOn="@string/bluetooth_car_already_paired"
                    android:textOff="@string/bluetooth_car_not_paired"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />
                <Button
                    android:id="@+id/bluetooth_pairing_response_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/bluetooth_pairing_response" />
                <EditText
                    android:id="@+id/bluetooth_auth_data_text"
                    android:layout_width="120dp"
                    android:layout_height="wrap_content"
                    android:inputType="textNoSuggestions" />
                <Button
                    android:id="@+id/bluetooth_auth_data_button"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/bluetooth_auth_data" />
            </LinearLayout>
        </LinearLayout>
    </ScrollView>

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="vertical" >
        <TextView
            android:id="@+id/navigation_label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/instrument_cluster_label"
            android:textSize="8pt" />
        <TextView
            android:id="@+id/navigation_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="@string/none" />
        <ImageView
            android:id="@+id/navigation_image"
            android:layout_width="@dimen/navigation_image"
            android:layout_height="@dimen/navigation_image"
            android:alpha="0.5"
            android:scaleType="fitCenter" />
        <TextView
            android:id="@+id/media_status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <ImageView
            android:id="@+id/media_album_art"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <TextView
            android:id="@+id/media_browser_root_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <TextView
            android:id="@+id/media_browser_source_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <TextView
            android:id="@+id/media_browser_list_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <TextView
            android:id="@+id/media_browser_song_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <TextView
            android:id="@+id/phone_status_text"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <ImageView
            android:id="@+id/phone_status_photo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>
</LinearLayout>


<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/native_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center"
    android:visibility="gone"
    android:background="#f0000000"
    android:orientation="vertical" >
    <TextView
        android:id="@+id/native_text"
        android:layout_width="match_parent"
        android:layout_height="0px"
        android:layout_weight="4"
        android:text="@string/native_content"
        android:textSize="100dp"
        android:gravity="center" />
    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:layout_weight="1" >
        <Button
            android:id="@+id/native_music_play"
            android:text="@string/label_music_play"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
        <Button
            android:id="@+id/native_music_stop"
            android:text="@string/label_music_stop"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </LinearLayout>
</LinearLayout>


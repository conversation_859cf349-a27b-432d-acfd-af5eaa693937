<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/connect_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="#ff3949ab">

    <RelativeLayout
        android:layout_width="0dp"
        android:layout_height="match_parent"
        android:layout_weight="0.5"
        android:layout_gravity="center_vertical">
        <ImageView
            android:id="@+id/connect_illustration"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_centerInParent="true"
            android:src="@drawable/waiting_for_connection"/>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_below="@id/connect_illustration"
            android:layout_alignLeft="@id/connect_illustration"
            android:layout_alignRight="@id/connect_illustration"
            android:layout_marginTop="16dp">
            <ListView
                android:id="@+id/connect_button_list"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_centerHorizontal="true">
            </ListView>
        </LinearLayout>
    </RelativeLayout>
</LinearLayout>

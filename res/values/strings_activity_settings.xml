<?xml version="1.0" encoding="utf-8"?>
<resources>

    <string name="title_activity_settings">Head Unit Settings</string>
    <!-- Strings related to Settings -->
    <string name="pref_screen_width">Width (pixels)</string>
    <string name="pref_screen_height">Height (pixels)</string>
    <string name="pref_screen_dpi">DPI</string>
    <string-array name="pref_preset_list_titles">
        <item>Native</item>
        <item>Custom</item>
        <item>800x480 7\" (133 dpi)</item>
        <item>800x480 6\" (155 dpi)</item>
        <item>800x480 ~6\" (160 dpi)</item>
        <item>1280x720 7\" (215 dpi)</item>
    </string-array>
    <string-array name="pref_preset_list_values">
        <item>0</item>
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>5</item>
    </string-array>
    <string name="pref_title_preset_screens">Screen preset</string>
    <string name="pref_scale_screen">Scale image to fit actual screen</string>
    <string name="pref_title_scale_screen">Scale to screen</string>
    <string-array name="pref_location_source_list_titles">
        <item>GPS</item>
        <item>Simulation</item>
    </string-array>
    <string name="pref_title_wifi_security">Wifi security type</string>
    <string-array name="pref_wifi_security_list_titles">
        <item>OPEN</item>
        <item>WEP_64</item>
        <item>WEP_128</item>
        <item>WPA_PERSONAL</item>
        <item>WPA2_PERSONAL</item>
        <item>WPA/WPA2_PERSONAL</item>
        <item>WPA_ENTERPRISE</item>
        <item>WPA2_ENTERPRISE</item>
        <item>WPA/WPA2_ENTERPRISE</item>
    </string-array>
    <string-array name="pref_wifi_security_list_values">
        <item>1</item>
        <item>2</item>
        <item>3</item>
        <item>4</item>
        <item>8</item>
        <item>12</item>
        <item>20</item>
        <item>24</item>
        <item>28</item>
    </string-array>

    <!-- Location data sources.
        Specified like this so we can refer to a default with @string/ -->
    <string name="pref_location_source_gps">gps</string>
    <string name="pref_location_source_simulation">simulation</string>
    <string-array name="pref_location_source_list_values">
        <item>@string/pref_location_source_gps</item>
        <item>@string/pref_location_source_simulation</item>
    </string-array>

    <string name="pref_title_location_source">Source of location data</string>
    <string name="pref_debug_mode">Enable debug controls. Use 800x480 + no scaling.</string>
    <string name="pref_title_debug_mode">Enable debug mode</string>

    <string name="pref_title_framerate_restrict">Fps 30</string>
    <string name="pref_framerate_restrict">Restrict frame rate to 30fps</string>

    <string name="pref_listen_tcp_port">TCP port to listen on</string>
    <string name="pref_car_wifi_ssid">Wifi network ssid</string>
    <string name="pref_car_wifi_password">Wifi network password</string>
    <string name="pref_wifi_auto_start">Automatically project to known device</string>

    <string name="pref_car_bluetooth_address">Car Bluetooth MAC address (e.g. 00:11:22:AA:BB:CC). Use SKIP_THIS_BLUETOOTH to skip Bluetooth pairing.</string>
    <string name="pref_car_supports_bluetooth_numeric_comparison">Car supports numeric comparison for BT pairing</string>
    <string name="pref_car_supports_bluetooth_pin">Car supports PIN for BT pairing</string>
    <string name="pref_rotary_controller_enabled">Emulate Rotary Controller</string>
    <string name="pref_touch_screen_enabled">Touch screen enabled</string>
    <string name="pref_touchpad_navigation_enabled">Allow touchpad navigation</string>
    <string name="pref_audio_focus_use_restricted_mode">Use restricted audio focus modes for testing</string>
    <string name="pref_generate_missing_sensors">Generate random data for all missing sensors</string>
    <string name="pref_use_tablet_compass_enabled">Use tablet\'s compass (unreliable)</string>

    <string name="pref_use_obd2_enabled">Use OBD2 over Bluetooth</string>
    <string name="pref_obd2_device">OBD2 Bluetooth device</string>

    <string name="no_bluetooth">Bluetooth support is missing or disabled</string>

    <string name="pref_use_aac_for_media_stream_enabled">Use AAC for Media (instead of PCM)</string>
    <string name="pref_use_aac_for_tts_stream_enabled">Use AAC for TTS (instead of PCM)</string>

    <string name="pref_auto_start_projection">Grant video focus on startup.</string>
    <string name="auto_start_local_mode">Auto start local mode</string>
    <string name="pref_use_sun_calculator">Day/Night mode based on sun position</string>
    <string name="pref_volume_toggle_debug">Volume buttons toggle debug panel</string>
    <string name="pref_show_vf_dialog">Show video focus request dialog</string>
</resources>

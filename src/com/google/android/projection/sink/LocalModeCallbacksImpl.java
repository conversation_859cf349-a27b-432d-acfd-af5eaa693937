// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink;

import com.google.android.projection.sink.ui.MainActivity;
import com.google.android.projection.sink.ui.MainActivity.LocalModeCallbacks;

/**
 * Disabled local mode hooks.
 */
public class LocalModeCallbacksImpl implements LocalModeCallbacks {
  // These are debug keys which let you validate that your port is working, once
  // you have all the agreements in place, we will grant you your production keys.
  // These keys are not guaranteed to work in production for an extended amount
  // of time. In production, you are responsible for obfuscating your keys so that
  // they cannot be recovered trivially.
  private static final String ROOT_CERTIFICATE =
      "-----BEGIN CERTIFICATE-----\n"
          + "MIIDiTCCAnGgAwIBAgIJAMFO56WkVE1CMA0GCSqGSIb3DQEBBQUAMFsxCzAJBgNV\n"
          + "BAYTAlVTMRMwEQYDVQQIDApDYWxpZm9ybmlhMRYwFAYDVQQHDA1Nb3VudGFpbiBW\n"
          + "aWV3MR8wHQYDVQQKDBZHb29nbGUgQXV0b21vdGl2ZSBMaW5rMB4XDTE0MDYwNjE4\n"
          + "MjgxOVoXDTQ0MDYwNTE4MjgxOVowWzELMAkGA1UEBhMCVVMxEzARBgNVBAgMCkNh\n"
          + "bGlmb3JuaWExFjAUBgNVBAcMDU1vdW50YWluIFZpZXcxHzAdBgNVBAoMFkdvb2ds\n"
          + "ZSBBdXRvbW90aXZlIExpbmswggEiMA0GCSqGSIb3DQEBAQUAA4IBDwAwggEKAoIB\n"
          + "AQDUH+iIbwwVb74NdI5eBv/ACFmh4ml/NOW7gUVWdYX50n8uQQsHHLCNIhk5VV2H\n"
          + "hanvAZ/XXHPuVAPadE2HpnNqePKF/RDo4eJo/+rOief8gBYq/Z+OQTZeLdNm+GoI\n"
          + "HBrEjU4Ms8IdLuFW0jF8LlIRgekjLHpc7duUl3QpwBlmAWQK40T/SZjprlmhyqfJ\n"
          + "g1rxFdnGbrSibmCsTmb3m6WZyZUyrcwmd7t6q3pHbMABO+o02asPG/YPj/SJo4+i\n"
          + "fb5/Nk56f3hH9pBiPKQXJnVUdVLKMXSRgydDBsGSBol4C0JL77MNDrMR5jdafJ4j\n"
          + "mWmsa2+mnzoAv9AxEL9T0LiNAgMBAAGjUDBOMB0GA1UdDgQWBBS5dqvv8DPQiwrM\n"
          + "fgn8xKR91k7wgjAfBgNVHSMEGDAWgBS5dqvv8DPQiwrMfgn8xKR91k7wgjAMBgNV\n"
          + "HRMEBTADAQH/MA0GCSqGSIb3DQEBBQUAA4IBAQDKcnBsrbB0Jbz2VGJKP2lwYB6P\n"
          + "dCTCCpQu7dVp61UQOX+zWfd2hnNMnLs/r1xPO+eyN0vmw7sD05phaIhbXVauKWZi\n"
          + "9WqWHTaR+9s6CTyBOc1Mye0DMj+4vHt+WLmf0lYjkYUVYvR1EImX8ktXzkVmOqn+\n"
          + "e30siqlZ8pQpsOgegIKfJ+pNQM8c3eXVv3KFMUgjZW33SziZL8IMsLvSO+1LtH37\n"
          + "KqbTEMP6XUwVuZopgGvaHU74eT/WSRGlL7vX4OL5/UXXP4qsGH2Zp7uQlErv4H9j\n"
          + "kMs37UL1vGb4M8RM7Eyu9/RulepSmqZUF+3i+3eby8iGq/3OWk9wgJf7AXnx\n"
          + "-----END CERTIFICATE-----\n";
  private static final String CLIENT_CERTIFICATE =
      "-----BEGIN CERTIFICATE-----\n"
          + "MIIDQDCCAigCAgEQMA0GCSqGSIb3DQEBCwUAMFsxCzAJBgNVBAYTAlVTMRMwEQYD\n"
          + "VQQIDApDYWxpZm9ybmlhMRYwFAYDVQQHDA1Nb3VudGFpbiBWaWV3MR8wHQYDVQQK\n"
          + "DBZHb29nbGUgQXV0b21vdGl2ZSBMaW5rMCYXETE0MDcwNDAwMDAwMC0wNzAwFxE0\n"
          + "ODA4MDExMDE2MjMtMDcwMDBoMQswCQYDVQQGEwJVUzETMBEGA1UECAwKQ2FsaWZv\n"
          + "cm5pYTEWMBQGA1UEBwwNTW91bnRhaW4gVmlldzEfMB0GA1UECgwWQW5kcm9pZC1B\n"
          + "dXRvLURldmVsb3BlcjELMAkGA1UECwwCMDEwggEiMA0GCSqGSIb3DQEBAQUAA4IB\n"
          + "DwAwggEKAoIBAQDVPJDd8tBSeKi+TQaJXITNXs52fDMMtfSaM69Gcw1r/FyS4y13\n"
          + "NZzs/lEbPcAAR1mA61wnLOUyB0p0uHc6dOgwjVAYtr6IFpylNVh+wuuGHM2hx4eq\n"
          + "t4F2Rfr/qLqYhAJxtA84kcmMNG3Z/xQ0fKOcFAdCTSIwxbet7Z5QqfC6zMcEu5k2\n"
          + "E6KnVMaCfAcWW1pl1HAVMjUsrGSuRLqPNmUdx9zNUNxKjNRb1wG4RIC5CAUABVyg\n"
          + "8AOQJh1H336WuVVzW9q+EpBgeUP/28biUirdo/3rT6b6j2hWIQ2bcT6OgyTGOCfg\n"
          + "XTW/OZK0ZH8ySkK3JGlVvj9gN8jjhC43Md41AgMBAAEwDQYJKoZIhvcNAQELBQAD\n"
          + "ggEBADuYShWdB6m1vJdNRC/mxF4So6Xe1CJIOFMCyCch13/vFklfsUqzO86WId9u\n"
          + "oGcZM3Sq6E9YPSP+tZ6jy1Zzw0IOmTI1z2/DLO9dX5Tz3e+XIjxpSa3WooNruhXE\n"
          + "I2Lz9WwaQyZBx3VW+2mbL354b9HYAJbZ+fyIBlNJdGmWoC7OejaV2shPYD56k7AK\n"
          + "qtXwdPLAlzWhOZdN6E0Td0d6S40S1jeoTXJYez3SVJdTyDf+0JjobZK0JZV/Mevw\n"
          + "fBkwgxaSa4Ukn+LY5wRDFaY5muQU9jDfPwvzGrxpI+fbUKomcWR4lKbhVdD29MLt\n"
          + "U+CUyIwMQC5rg/5bpFjsn19b0RA=\n"
          + "-----END CERTIFICATE-----\n";

  private static final String PRIVATE_KEY =
      "-----BEGIN PRIVATE KEY-----\n"
          + "MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQDVPJDd8tBSeKi+\n"
          + "TQaJXITNXs52fDMMtfSaM69Gcw1r/FyS4y13NZzs/lEbPcAAR1mA61wnLOUyB0p0\n"
          + "uHc6dOgwjVAYtr6IFpylNVh+wuuGHM2hx4eqt4F2Rfr/qLqYhAJxtA84kcmMNG3Z\n"
          + "/xQ0fKOcFAdCTSIwxbet7Z5QqfC6zMcEu5k2E6KnVMaCfAcWW1pl1HAVMjUsrGSu\n"
          + "RLqPNmUdx9zNUNxKjNRb1wG4RIC5CAUABVyg8AOQJh1H336WuVVzW9q+EpBgeUP/\n"
          + "28biUirdo/3rT6b6j2hWIQ2bcT6OgyTGOCfgXTW/OZK0ZH8ySkK3JGlVvj9gN8jj\n"
          + "hC43Md41AgMBAAECggEAUvw0ILxbiIz+lrjhP1nrMQWhcgkZ0OuLGuvcXZ1u7sSS\n"
          + "vnN9ATSzMqQyqlhYvYMG+R4fift14wdlWZFkjTJsHmEQd92+vrWWFtFw9DwlbtcM\n"
          + "y9YcHTs3PfE0IoTmwW42iUua1NgfyNmqmrkjgt2yyo4c2Reto69+sUiVYS0Ry5Aq\n"
          + "R6o92PLyDt4Z9mXC54U0OkPef/3qMx/o8xPZY+i0Wi3hWAGZ2NVvS3GDHUVWU1O4\n"
          + "AaZ0sJpPsdfvgpeiWDEdO9Pcd+YNo3KO3wxCcKle1E7Ve4RwjMtk/P3+HOWQcagn\n"
          + "zQ4xUkeh0On6DB+uKHU7Z2rZR0cLJX/L2g/QRbSeoQKBgQDvRIPVLumIHmmp+umC\n"
          + "2wiHc2DepZzznSCtWcYIDT1woPzEtJ3nW8B9gYvvP96zVSXgLIBpwKn4Z0N8h9Sl\n"
          + "+fnj8C3p4rxb8w/2PCbNueH7Hk6HlnjoJcJfhdRuGiClEJ7NIUsW0Oeq6mAGg+fz\n"
          + "k2nOpixTnyNXlXDZZWlerzB3vQKBgQDkJgfK5s50FssVlCv+OLua+YRFvxLpFP1o\n"
          + "REv8A00syZ24c+NT5qADdFY6XEnxZatoSvzwzDqtrmeJoQvACGcZ8CIA6zXekuNx\n"
          + "nxmKpg5opElt/zjPBNwc0GPMSt2zJQ5p9clCjzGRsQJE5NGElUb5p9D7lceyif9N\n"
          + "GfTYsKhL2QKBgEsilNOJG8Sm6D98j3+MEK2YSb8g+px1pyFIo6s7nSOVKksK/Nr+\n"
          + "MQA4ghzrTZ+7CV3QG7MZ0UY0Bzk/3kMQsKwnbAOaM9QcC9MjKHxJ9vDIn5hsZCNi\n"
          + "WBtvBzpb8gxjn7qCzfWoPFerqePh4CDZFczS2fCYdJy4TOu3sNMO6IVxAoGASMZc\n"
          + "y/sttdgjdcGq+s/MABZBfCID/TeF0cGfX0cKUhQJ7HGC/RtexVtRuhIAQDFHKOBq\n"
          + "qMDsosQ0ILGvVtUXVi3EGvjsWRAP3wl6E0D+7aAiECVOULLjJ4nNm7fx2hgi7efK\n"
          + "CCI3FJsa9zDqKFmZpz9BqU7QwYHFXym9cXWAaZkCgYAHwrlr8WN105nTbT9WSaY4\n"
          + "DSzjGwBn28myz4PcKDMyEybojhhGHmznMrMrdsPwh0s0oYFWCVntedUrUdYW3Fxg\n"
          + "Rx2tbxrEGUxxiEOWP+dN2jnzRUnK50K/2xvRFh59hHCwYMtpNe3trn9AQSgVfYWP\n"
          + "v49r6mcqWoJ24OVHF0amiw==\n"
          + "-----END PRIVATE KEY-----\n";

    @Override
    public boolean isLocalMode() {
        return false;
    }

    @Override
    public void startLocalMode(MainActivity activity) {
        throw new RuntimeException("Local mode not supported in this build.");
    }

    @Override
    public String getRootCert() {
        return ROOT_CERTIFICATE;
    }

    @Override
    public String getClientCert() {
        return CLIENT_CERTIFICATE;
    }

    @Override
    public String getPrivateKey() {
        return PRIVATE_KEY;
    }
}

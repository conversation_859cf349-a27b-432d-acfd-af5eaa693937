// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.transport.usb;

import android.content.Context;
import android.hardware.usb.UsbConstants;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbDeviceConnection;
import android.hardware.usb.UsbInterface;
import android.hardware.usb.UsbManager;
import android.util.Log;
import com.google.android.projection.protocol.AndroidHeadUnitLogging;
import java.util.HashSet;
import java.util.Set;

/**
 * Sets up the USB connection and finishes itself.
 */
public final class UsbHostSetup {
    private static final String TAG = AndroidHeadUnitLogging.TAG_USB_HOST_CONNECTION;

    private static final Set<Integer> VENDOR_IDS = new HashSet<Integer>();
    static {
        VENDOR_IDS.add(0x18d1); // Google
        VENDOR_IDS.add(0x04E8); // Samsung
        VENDOR_IDS.add(0x22b8); // Motorola
        VENDOR_IDS.add(0x0bb4); // HTC
        VENDOR_IDS.add(0x1004); // LGE
    }
    private static final int ACCESSORY_PRODUCT_ID0 = 0x2d00;
    private static final int ACCESSORY_PRODUCT_ID1 = 0x2d01;

    private static final int AOAP_STR_MANUFACTURER_NAME = 0;
    private static final int AOAP_STR_MODEL_NAME = 1;
    private static final int AOAP_STR_DESCRIPTION = 2;
    private static final int AOAP_STR_VERSION = 3;
    private static final int AOAP_STR_URI = 4;
    private static final int AOAP_STR_SERIAL_NUMBER = 5;

    private static final int USB_TIMEOUT_MS = 100 * 1000;

    private static final String ACCESSORY_MANUFACTURER_NAME = "Android";
    private static final String ACCESSORY_MODEL_NAME = "Android Auto";
    private static final String ACCESSORY_DESCRIPTION = "Android Auto";
    private static final String ACCESSORY_VERSION = "1.0";
    private static final String ACCESSORY_URI = "http://www.android.com/auto";
    private static final String ACCESSORY_SERIAL_NUMBER = "0000000012345678";

    private UsbHostSetup() {
    }

    public static boolean processDevice(UsbDevice device, Context context) {
        boolean isAndroidAccessory = (VENDOR_IDS.contains(device.getVendorId())
                && (device.getProductId() == ACCESSORY_PRODUCT_ID0
                        || device.getProductId() == ACCESSORY_PRODUCT_ID1));

        Log.d(TAG, "processDevice(), isAndroidAccessory = " + isAndroidAccessory);

        if (!isAndroidAccessory) {
            // Step 1.
            // On initial connection we may need to probe the accessory
            // to see if it is Android. Perform probe.
            UsbInterface usbInterface = device.getInterface(0);
            UsbManager usbManager = (UsbManager) context.getSystemService(Context.USB_SERVICE);
            UsbDeviceConnection connection = usbManager.openDevice(device);
            if (connection == null) {
                return false;
            }
            connection.claimInterface(usbInterface, true);
            switchAccessory(connection);
            // At this point we finish the activity, but may get
            // re-launched with valid Android accessory ID...
            return false;
        } else {
            // Step 2.
            // If the probe was successful, we can now start
            // a USB host connection.
            return true;
        }
    }

    private static boolean switchAccessory(UsbDeviceConnection connection) {
        int protocolVersion = getAccessoryProtocol(connection);
        if (protocolVersion <= 0) {
            Log.d(TAG, String.format("Incorrect accessory protocol %d", protocolVersion));
            return false;
        }
        if (!sendAccessoryString(
                connection, AOAP_STR_MANUFACTURER_NAME, ACCESSORY_MANUFACTURER_NAME)) {
            Log.d(TAG, "Unable to send manufacturer name");
            return false;
        }
        if (!sendAccessoryString(connection, AOAP_STR_MODEL_NAME, ACCESSORY_MODEL_NAME)) {
            Log.d(TAG, "Unable to send model name");
            return false;
        }
        if (!sendAccessoryString(connection, AOAP_STR_DESCRIPTION, ACCESSORY_DESCRIPTION)) {
            Log.d(TAG, "Unable to send description");
            return false;
        }
        if (!sendAccessoryString(connection, AOAP_STR_VERSION, ACCESSORY_VERSION)) {
            Log.d(TAG, "Unable to send version");
            return false;
        }
        if (!sendAccessoryString(connection, AOAP_STR_URI, ACCESSORY_URI)) {
            Log.d(TAG, "Unable to send URI");
            return false;
        }
        if (!sendAccessoryString(connection, AOAP_STR_SERIAL_NUMBER, ACCESSORY_SERIAL_NUMBER)) {
            Log.d(TAG, "Unable to send serial number");
            return false;
        }
        if (!startAccessory(connection)) {
            Log.d(TAG, "Unable to start accessory mode");
            return false;
        }
        return true;
    }

    private static boolean sendAccessoryString(UsbDeviceConnection conn, int index, String str) {
        return sendAccessoryControlTransfer(conn, 52, index, str.getBytes()) == str.length();
    }

    private static boolean startAccessory(UsbDeviceConnection conn) {
        return sendAccessoryControlTransfer(conn, 53, 0, null) == 0;
    }

    /**
     * Returns the protocol version, or -1 on failure.
     */
    private static int getAccessoryProtocol(UsbDeviceConnection conn) {
        byte[] buffer = new byte[2];

        int bytesReceived = conn.controlTransfer(
                UsbConstants.USB_DIR_IN | UsbConstants.USB_TYPE_VENDOR,
                51, 0, 0, buffer, 2, USB_TIMEOUT_MS);
        if (bytesReceived == 2) {
            return buffer[0] + (buffer[1] << 8);
        }

        return -1;
    }

    private static int sendAccessoryControlTransfer(UsbDeviceConnection conn, int request,
            int index, byte[] buffer) {
        return conn.controlTransfer(
                UsbConstants.USB_DIR_OUT | UsbConstants.USB_TYPE_VENDOR, request,
                0, index, buffer, buffer == null ? 0 : buffer.length, USB_TIMEOUT_MS);
    }
}

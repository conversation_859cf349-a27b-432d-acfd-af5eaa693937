package com.google.android.projection.sink.sensors;

import android.os.Handler;
import android.os.Message;

import java.util.HashSet;
import java.util.Set;

/**
 * Base class for implementing sensor source feeds.
 */
public abstract class SensorCollectorBase {
    private final Set<Integer> mRegisterdSensors = new HashSet<Integer>();
    private Handler mHandler;

    public final Set<Integer> discoverSensors() {
        Set<Integer> sensors = new HashSet<Integer>();
        discoverSensorsImpl(sensors);
        return sensors;
    }

    public final void registerSensors(Set<Integer> sensors, Handler handler) {
        mHandler = handler;
        mRegisterdSensors.addAll(sensors);
        registerSensorsImpl();
    }

    public final void unregisterSensors() {
        unregisterSensorsImpl();
        mRegisterdSensors.clear();
        mHandler = null;
    }

    public final boolean sendUpdate(SensorSinkWrapper sink, int sensorType) {
        if (!isHandled(sensorType)) {
            return false;
        }
        return sendUpdateImpl(sink, sensorType);
    }

    public void shutdown() {
    }

    protected boolean isHandled(int sensorType) {
        return mRegisterdSensors.contains(sensorType);
    }

    protected void reportAvailable(int sensorType) {
        if (mHandler != null) {
            mHandler.sendMessage(Message.obtain(mHandler, sensorType));
        }
    }

    protected void post(Runnable r) {
        if (mHandler != null) {
            mHandler.post(r);
        }
    }

    protected void postDelayed(Runnable r, long delayMs) {
        if (mHandler != null) {
            mHandler.postDelayed(r, delayMs);
        }
    }

    protected void removeCallbacks(Runnable r) {
        if (mHandler != null) {
            mHandler.removeCallbacks(r);
        }
    }

    protected abstract void discoverSensorsImpl(Set<Integer> sensors);
    protected abstract void registerSensorsImpl();
    protected abstract void unregisterSensorsImpl();
    protected abstract boolean sendUpdateImpl(SensorSinkWrapper sink, int sensorType);
}

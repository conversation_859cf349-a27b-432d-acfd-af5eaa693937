package com.google.android.projection.sink.sensors;

import android.content.Context;
import android.location.GpsSatellite;
import android.location.GpsStatus;
import android.location.Location;
import android.location.LocationListener;
import android.location.LocationManager;
import android.os.Bundle;
//import com.google.android.gms.annotation.Permissions;
//import com.google.android.gms.annotation.UsesPermission;
import com.google.android.projection.proto.Protos.DrivingStatus;
import com.google.android.projection.protocol.SensorSource;
import com.google.android.projection.sink.debug.DebugContext;
import java.util.Set;

/**
 * Collect tablet's GPS-only location.
 */
public class LocationSensors extends SensorCollectorBase {
    private final DebugContext mDebugContext;
//    private final LocationManager mLocationManager;
    private Location mLocation;
    private GpsStatus mGpsStatus;

    public LocationSensors(Context context, DebugContext debugContext) {
        mDebugContext = debugContext;
//        mLocationManager = (LocationManager) context.getSystemService(Context.LOCATION_SERVICE);
    }

    @Override
    protected void discoverSensorsImpl(Set<Integer> sensors) {
//        if (mLocationManager.isProviderEnabled(LocationManager.GPS_PROVIDER)) {
//            sensors.add(SensorSource.SENSOR_TYPE_LOCATION);
//            sensors.add(SensorSource.SENSOR_TYPE_GPS_SATELLITE_DATA);
//            sensors.add(SensorSource.SENSOR_TYPE_DRIVING_STATUS);
//        }
    }

  @Override
  //@UsesPermission(Permissions.ACCESS_FINE_LOCATION)
  protected void registerSensorsImpl() {
        if (isHandled(SensorSource.SENSOR_TYPE_GPS_SATELLITE_DATA)) {
            //mLocationManager.addGpsStatusListener(mGpsStatusListener);
        }
        if (isHandled(SensorSource.SENSOR_TYPE_LOCATION)) {
            //mLocationManager.requestLocationUpdates(LocationManager.GPS_PROVIDER, 0, 0,
            //        mLocationListener);
        }
    }

    @Override
    protected void unregisterSensorsImpl() {
//        mLocationManager.removeUpdates(mLocationListener);
//        mLocationManager.removeGpsStatusListener(mGpsStatusListener);
    }

    @Override
    protected boolean sendUpdateImpl(SensorSinkWrapper sink, int sensorType) {
        if (sensorType == SensorSource.SENSOR_TYPE_GPS_SATELLITE_DATA) {
            if (mGpsStatus == null) {
                return false;
            }

            int numberInView = 0;
            int numberInUse = 0;
            for (GpsSatellite satellite : mGpsStatus.getSatellites()) {
                ++numberInView;
                if (satellite.usedInFix()) {
                    ++numberInUse;
                }
            }
            int[] prn = new int[numberInView];
            float[] snr = new float[numberInView];
            boolean[] usedInFix = new boolean[numberInView];
            float[] azimuth = new float[numberInView];
            float[] elevation = new float[numberInView];
            int i = 0;
            for (GpsSatellite satellite : mGpsStatus.getSatellites()) {
                prn[i] = satellite.getPrn();
                snr[i] = satellite.getSnr();
                usedInFix[i] = satellite.usedInFix();
                azimuth[i] = satellite.getAzimuth();
                elevation[i] = satellite.getElevation();
            }

            sink.reportGpsSatelliteData(numberInUse, true, numberInView, prn, snr, usedInFix,
                    azimuth, elevation);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_LOCATION) {
            if (mLocation == null) {
                return false;
            }
            sink.reportLocation(mLocation);
            reportKeyboardLockout(mDebugContext, sink, mLocation);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_DRIVING_STATUS) {
            reportKeyboardLockout(mDebugContext, sink, mLocation);
            return true;
        }
        throw new RuntimeException();
    }

  private final GpsStatus.Listener mGpsStatusListener =
      new GpsStatus.Listener() {
        @Override
        //@UsesPermission(Permissions.ACCESS_FINE_LOCATION)
        public void onGpsStatusChanged(int event) {
//          if (event == GpsStatus.GPS_EVENT_SATELLITE_STATUS) {
//            mGpsStatus = mLocationManager.getGpsStatus(mGpsStatus);
//            reportAvailable(SensorSource.SENSOR_TYPE_GPS_SATELLITE_DATA);
//          }
        }
      };

    private final LocationListener mLocationListener = new LocationListener() {
        @Override
        public void onLocationChanged(Location location) {
            mLocation = location;
            reportAvailable(SensorSource.SENSOR_TYPE_LOCATION);
        }

        @Override
        public void onProviderEnabled(String provider) {
        }

        @Override
        public void onProviderDisabled(String provider) {
        }

        @Override
        public void onStatusChanged(String provider, int status, Bundle extras) {
        }
    };

    static void reportKeyboardLockout(
            DebugContext debugContext, SensorSinkWrapper sink, Location location) {
        // For testing purpose only.
        switch (debugContext.getKeyboardLockoutMode()) {
            case DebugContext.KEYBOARD_LOCKOUT_AUTO:
                if (location == null
                        || !location.hasSpeed()
                        || (location.hasSpeed() && location.getSpeed() < 1.3)) {
                    // No location data or less than ~5km/h - allow keyboard.
                    sink.reportDrivingStatusData(
                        DrivingStatus.DRIVE_STATUS_UNRESTRICTED.getNumber());
                } else {
                    // Confirmed driving above 5km/h - disallow keyboard.
                    sink.reportDrivingStatusData(
                        DrivingStatus.DRIVE_STATUS_NO_KEYBOARD_INPUT.getNumber());
                }
                break;
            case DebugContext.KEYBOARD_LOCKOUT_FORCE:
                sink.reportDrivingStatusData(
                    DrivingStatus.DRIVE_STATUS_NO_KEYBOARD_INPUT.getNumber());
                break;
            case DebugContext.KEYBOARD_LOCKOUT_OVERRIDE:
                sink.reportDrivingStatusData(
                    DrivingStatus.DRIVE_STATUS_UNRESTRICTED.getNumber());
                break;
        }
    }
}

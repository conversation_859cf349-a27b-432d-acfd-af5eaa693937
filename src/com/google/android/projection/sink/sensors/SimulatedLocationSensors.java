package com.google.android.projection.sink.sensors;

import android.content.Context;
import android.location.Location;
import android.location.LocationListener;
import android.os.Bundle;
import com.google.android.projection.proto.Protos.DrivingStatus;
import com.google.android.projection.protocol.SensorSource;
import com.google.android.projection.sink.debug.DebugContext;
import com.google.android.projection.sink.debug.SimulatedLocationProvider;
import com.google.android.projection.sink.settings.Settings;
import java.util.Set;

/**
 * Simulated location provider. TESTING ONLY.
 */
public class SimulatedLocationSensors extends SensorCollectorBase {
    private static final int GPS_UPDATE_INTERVAL = 1000;

    private final DebugContext mDebugContext;
    private final SimulatedLocationProvider mLocationProvider;
    private final boolean mEnabled;
    private final boolean mSimulateMissingAccuracy;
    private final boolean mSimulateMissingBearing;
    private final boolean mSimulateMissingSpeed;
    private Location mLocation;

    public SimulatedLocationSensors(Context context, DebugContext debugContext) {
        mDebugContext = debugContext;
        if (context != null) {
            mLocationProvider = debugContext.getSimulatedLocationProvider();
            Settings settings = new Settings(context);
            mEnabled = settings.isGpsSimulationEnabled();
            mSimulateMissingAccuracy = settings.isSimulateMissingAccuracyEnabled();
            mSimulateMissingBearing = settings.isSimulateMissingBearingEnabled();
            mSimulateMissingSpeed = settings.isSimulateMissingSpeedEnabled();
        } else {
            mLocationProvider = null;
            mEnabled = true;
            mSimulateMissingAccuracy = false;
            mSimulateMissingBearing = false;
            mSimulateMissingSpeed = false;
        }
    }

    @Override
    protected void discoverSensorsImpl(Set<Integer> sensors) {
        if (mEnabled) {
            sensors.add(SensorSource.SENSOR_TYPE_LOCATION);
            sensors.add(SensorSource.SENSOR_TYPE_GPS_SATELLITE_DATA);
            sensors.add(SensorSource.SENSOR_TYPE_DRIVING_STATUS);
        }
    }

    @Override
    protected void registerSensorsImpl() {
        if (isHandled(SensorSource.SENSOR_TYPE_GPS_SATELLITE_DATA)) {
            post(mGpsStatusUpdate);
        }
        if (isHandled(SensorSource.SENSOR_TYPE_LOCATION)) {
            mLocationProvider.requestUpdates(mLocationListener);
        }
    }

    @Override
    protected void unregisterSensorsImpl() {
        if (mLocationProvider != null) {
            mLocationProvider.removeUpdates(mLocationListener);
        }
        removeCallbacks(mGpsStatusUpdate);
    }

    @Override
    protected boolean sendUpdateImpl(SensorSinkWrapper sink, int sensorType) {
        if (sensorType == SensorSource.SENSOR_TYPE_GPS_SATELLITE_DATA) {
            sink.reportGpsSatelliteData(6, true, 8, null, null, null, null, null);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_LOCATION) {
            sink.reportLocation(mLocation);
            LocationSensors.reportKeyboardLockout(mDebugContext, sink, mLocation);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_DRIVING_STATUS) {
            LocationSensors.reportKeyboardLockout(mDebugContext, sink, mLocation);
            return true;
        }
        throw new RuntimeException();
    }

    private final Runnable mGpsStatusUpdate = new Runnable() {
        @Override
        public void run() {
            reportAvailable(SensorSource.SENSOR_TYPE_GPS_SATELLITE_DATA);
            postDelayed(this, GPS_UPDATE_INTERVAL);
        }
    };

    private final LocationListener mLocationListener = new LocationListener() {
        @Override
        public void onLocationChanged(Location location) {
            if (location != null) {
                if (mSimulateMissingAccuracy) {
                    location.removeAccuracy();
                }
                if (mSimulateMissingBearing) {
                    location.removeBearing();
                }
                if (mSimulateMissingSpeed) {
                    location.removeSpeed();
                }
                mLocation = location;
                reportAvailable(SensorSource.SENSOR_TYPE_LOCATION);
            }
        }

        @Override
        public void onStatusChanged(String provider, int status, Bundle extras) {
        }

        @Override
        public void onProviderEnabled(String provider) {
        }

        @Override
        public void onProviderDisabled(String provider) {
        }
    };
}

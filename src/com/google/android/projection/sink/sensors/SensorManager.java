package com.google.android.projection.sink.sensors;

import android.location.Location;
import android.os.Handler;
import android.os.Message;
import com.google.android.projection.protocol.SensorSource;
import com.google.android.projection.sink.GalIntegration;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * Manages several sources of sensor data and triggers periodic updates. Allows subscribers to get
 * this data. Sends sensors to the GAL.
 */
public class SensorManager {

    /**
     * Listener for location events.
     */
    public interface LocationListener {
        void onLocationChanged(Location location);
    }

    /**
     * Listener for compass events.
     */
    public interface CompassListener {
        void onCompassChanged(float azimuth, float pitch, float roll);
    }

    private final Set<Integer> mSupportedSensors = new HashSet<Integer>();
    private final Handler mHandler = new SensorHandler();
    private final List<SensorCollectorBase> mSensorFeeds = new ArrayList<SensorCollectorBase>();

    private final Object mGalLock = new Object();
    private GalIntegration mGal;
    private SensorsInterceptor mSensorsInterceptor;
    private long mUpdateCounter;

  private final List<LocationListener> mLocationListeners = new CopyOnWriteArrayList<>();
  private final List<CompassListener> mCompassListeners = new CopyOnWriteArrayList<>();

    public boolean isReady() {
        synchronized (mGalLock) {
            return mGal != null;
        }
    }

    public void setGalIntegration(GalIntegration gal) {
        synchronized (mGalLock) {
            mGal = gal;
            if (mGal != null) {
                mSensorsInterceptor = new SensorsInterceptor(mGal.sensors);
            } else {
                mSensorsInterceptor = null;
            }
        }
    }

    public void init(SensorCollectorBase... collectors) {
        for (SensorCollectorBase collector : collectors) {
            mSensorFeeds.add(collector);
        }

        // Init all sensors.
        for (SensorCollectorBase feed : mSensorFeeds) {
            Set<Integer> sensors = feed.discoverSensors();
            // Register only new sensors. This allows to mock sensors by "discovering" them earlier.
            sensors.removeAll(mSupportedSensors);
            feed.registerSensors(sensors, mHandler);
            mSupportedSensors.addAll(sensors);
        }

        // Start loop.
        mHandler.sendMessage(Message.obtain(mHandler, 0));
    }

    public void shutdown() {
        mHandler.removeCallbacksAndMessages(null);
        for (SensorCollectorBase feed : mSensorFeeds) {
            feed.unregisterSensors();
        }
        synchronized (mGalLock) {
            mGal = null;
        }
        for (SensorCollectorBase feed : mSensorFeeds) {
            feed.shutdown();
        }
    }

    public List<Integer> getSupportedSensors() {
        return new ArrayList<Integer>(mSupportedSensors);
    }

    public void registerLocationListener(LocationListener listener) {
        mLocationListeners.add(listener);
    }

    public void unregisterLocationListener(LocationListener listener) {
        mLocationListeners.remove(listener);
    }

    public void registerCompassListener(CompassListener listener) {
        mCompassListeners.add(listener);
    }

    public void unregisterCompassListener(CompassListener listener) {
        mCompassListeners.remove(listener);
    }

    public void reportAvailable(int sensorType) {
        mHandler.sendMessage(Message.obtain(mHandler, sensorType));
    }

    private void sendUpdate(int sensor) {
        if (mSupportedSensors.contains(sensor)) {
            for (SensorCollectorBase feed : mSensorFeeds) {
                if (feed.sendUpdate(mSensorsInterceptor, sensor)) {
                    break;
                }
            }
        }
    }

    private void sendRegularUpdatesLocked() {
        // Send high frequency (10 Hz) updates.
        if (mGal != null && mGal.sensors != null) {
            sendUpdate(SensorSource.SENSOR_TYPE_SPEED);
            sendUpdate(SensorSource.SENSOR_TYPE_COMPASS);
            sendUpdate(SensorSource.SENSOR_TYPE_ACCELEROMETER_DATA);
            sendUpdate(SensorSource.SENSOR_TYPE_GYROSCOPE_DATA);
            sendUpdate(SensorSource.SENSOR_TYPE_RPM);

            if ((mUpdateCounter % 10) == 0) {
                // Send medium frequency (1 Hz) updates.
                sendUpdate(SensorSource.SENSOR_TYPE_DEAD_RECKONING_DATA);
            }

            if ((mUpdateCounter % 100) == 0) {
                // Send low frequency (0.1 Hz) updates.
                sendUpdate(SensorSource.SENSOR_TYPE_ENVIRONMENT);
            }

            if (mUpdateCounter == 0) {
                // First update. Send all on-change initial data.
                sendUpdate(SensorSource.SENSOR_TYPE_DRIVING_STATUS);
                sendUpdate(SensorSource.SENSOR_TYPE_LOCATION);
                sendUpdate(SensorSource.SENSOR_TYPE_GPS_SATELLITE_DATA);
                sendUpdate(SensorSource.SENSOR_TYPE_NIGHT_MODE);
                sendUpdate(SensorSource.SENSOR_TYPE_GEAR);
                sendUpdate(SensorSource.SENSOR_TYPE_ODOMETER);
                sendUpdate(SensorSource.SENSOR_TYPE_FUEL);
                sendUpdate(SensorSource.SENSOR_TYPE_PARKING_BRAKE);
                sendUpdate(SensorSource.SENSOR_TYPE_DOOR_DATA);
                sendUpdate(SensorSource.SENSOR_TYPE_HVAC);
                sendUpdate(SensorSource.SENSOR_TYPE_LIGHT_DATA);
                sendUpdate(SensorSource.SENSOR_TYPE_OBDII_DIAGNOSTIC_CODE);
                sendUpdate(SensorSource.SENSOR_TYPE_PASSENGER_DATA);
                sendUpdate(SensorSource.SENSOR_TYPE_TIRE_PRESSURE_DATA);
            }

            ++mUpdateCounter;
        }
        mHandler.sendMessageDelayed(Message.obtain(mHandler, 0), 100);
    }

    private class SensorHandler extends Handler {
        @Override
        public void handleMessage(Message message) {
            synchronized (mGalLock) {
                if (message.what == 0) {
                    sendRegularUpdatesLocked();
                } else {
                    if (mGal != null && mGal.sensors != null) {
                        sendUpdate(message.what);
                    }
                }
            }
        }
    }

    private class SensorsInterceptor extends SensorSinkWrapper {
        public SensorsInterceptor(SensorSource sensors) {
            super(sensors);
        }

        @Override
        public void reportLocation(final Location location) {
            super.reportLocation(location);
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    for (LocationListener listener : mLocationListeners) {
                        listener.onLocationChanged(location);
                    }
                }
            });
        }

        @Override
        public void reportCompassData(final int bearing, final boolean hasPitch, final int intPitch,
                final boolean hasRoll, final int intRoll) {
            super.reportCompassData(bearing, hasPitch, intPitch, hasRoll, intRoll);
            mHandler.post(new Runnable() {
                @Override
                public void run() {
                    float azimuth = bearing;
                    float pitch = hasPitch ? intPitch : Float.NaN;
                    float roll = hasRoll ? intRoll : Float.NaN;
                    for (CompassListener listener : mCompassListeners) {
                        listener.onCompassChanged(azimuth, pitch, roll);
                    }
                }
            });
        }
    }
}

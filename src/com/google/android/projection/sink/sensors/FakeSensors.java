package com.google.android.projection.sink.sensors;

import android.content.Context;
import com.google.android.projection.protocol.SensorSource;
import com.google.android.projection.sink.settings.Settings;
import java.util.Random;
import java.util.Set;

/**
 * Provides some set of sensors with random data. TESTING ONLY.
 *
 * <p>Speed is adjusted in a way to prevent large changes (so it only increases/decreases) and
 * the odometer is adjusted to be coherent with the speed. Everything else is just absolutely
 * random with some slight attempt to use a correct range.
 */
public class FakeSensors extends SensorCollectorBase {
    private static final float KPH_PER_MPS = 3.6F;

    private final Random mRandom = new Random(System.currentTimeMillis());
    private final boolean mEnabled;

    private float mOdometer;
    private float mReportedOdometer;
    private long mLastSpeedUpdate;
    private int mSpeed;

    public FakeSensors(Context context) {
        if (context != null) {
            Settings settings = new Settings(context);
            mEnabled = settings.isMissingSensorsGeneratorEnabled();
        } else {
            mEnabled = true;
        }

        if (mEnabled) {
            mOdometer = mRandom.nextInt(100000);
            mSpeed = mRandom.nextInt((int)(120F / KPH_PER_MPS));
        }
    }

    @Override
    protected void discoverSensorsImpl(Set<Integer> sensors) {
        if (mEnabled) {
            for (int sensor : PERIODIC_SENSORS) {
                sensors.add(sensor);
            }
            for (int sensor : ONCHANGE_SENSORS) {
                sensors.add(sensor);
            }
        }
    }

    @Override
    protected void registerSensorsImpl() {
        post(mChangeSomeFakeValues);
    }

    @Override
    protected void unregisterSensorsImpl() {
        removeCallbacks(mChangeSomeFakeValues);
    }

    @Override
    protected boolean sendUpdateImpl(SensorSinkWrapper sink, int sensorType) {
        // So disappointed about non-const finals in android libraries...
        if (sensorType == SensorSource.SENSOR_TYPE_SPEED) {
            mSpeed += (mRandom.nextInt(30) - 15);
            if (mSpeed < 0) {
                mSpeed = 0;
            } else if (mSpeed > 200 / KPH_PER_MPS) {
                mSpeed = (int) (200 / KPH_PER_MPS);
            }
            long now = System.currentTimeMillis();
            if (mLastSpeedUpdate > 0) {
                float time = (float) (now - mLastSpeedUpdate) / 1000;
                float distanceKm = mSpeed * time / 1000;
                mOdometer += distanceKm;
                if (mOdometer - mReportedOdometer > 0.1f) {
                    reportAvailable(SensorSource.SENSOR_TYPE_ODOMETER);
                }
            }
            mLastSpeedUpdate = now;
            sink.reportSpeedData(mSpeed);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_ODOMETER) {
            mReportedOdometer = mOdometer;
            sink.reportOdometerData(mReportedOdometer);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_RPM) {
            sink.reportRpmData(mRandom.nextInt(8500));
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_FUEL) {
            int fuelPercent = mRandom.nextInt(100);
            sink.reportFuelData(fuelPercent, mRandom.nextInt(500), fuelPercent < 10);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_PARKING_BRAKE) {
            sink.reportParkingBrakeData(mRandom.nextBoolean());
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_GEAR) {
            sink.reportGearData(mRandom.nextInt(6));
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_ENVIRONMENT) {
            sink.reportEnvironmentData(20 + mRandom.nextInt(20), mRandom.nextInt(20) + 90);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_HVAC) {
            sink.reportHvacData(20 + mRandom.nextInt(20), 20 + mRandom.nextInt(20));
            return true;
        }
        throw new RuntimeException();
    }

    private final Runnable mChangeSomeFakeValues = new Runnable() {
        @Override
        public void run() {
            for (int sensor : ONCHANGE_SENSORS) {
                // Odometer is handled differently.
                if (sensor != SensorSource.SENSOR_TYPE_ODOMETER) {
                    reportAvailable(sensor);
                }
            }
            // Schedule next fake "on-change" update.
            postDelayed(mChangeSomeFakeValues, 1000 + mRandom.nextInt(1000));
        }
    };

    private static final int[] PERIODIC_SENSORS = {
        SensorSource.SENSOR_TYPE_SPEED,
        SensorSource.SENSOR_TYPE_RPM,
        SensorSource.SENSOR_TYPE_ENVIRONMENT,
    };

    private static final int[] ONCHANGE_SENSORS = {
        SensorSource.SENSOR_TYPE_ODOMETER,
        SensorSource.SENSOR_TYPE_FUEL,
        SensorSource.SENSOR_TYPE_PARKING_BRAKE,
        SensorSource.SENSOR_TYPE_GEAR,
        SensorSource.SENSOR_TYPE_HVAC,
    };
}

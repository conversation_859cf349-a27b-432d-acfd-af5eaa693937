package com.google.android.projection.sink.sensors;

import android.content.Context;
import android.hardware.Sensor;
import android.hardware.SensorEvent;
import android.hardware.SensorEventListener;
import android.hardware.SensorManager;
import android.location.Location;
import android.location.LocationManager;
import android.os.Handler;
import android.text.format.DateUtils;
import android.util.Log;
import android.view.Display;
import android.view.Surface;
import android.view.WindowManager;
//import com.google.android.gms.annotation.Permissions;
//import com.google.android.gms.annotation.UsesPermission;
import com.google.android.projection.protocol.SensorSource;
import com.google.android.projection.sink.settings.Settings;
import java.util.Set;

/**
 * Collect sensors available on-board on the tablet except GPS location
 * which is handled separately in {@link LocationSensors}.
 */
public class TabletSensors extends SensorCollectorBase {
    private static final String TAG = "AHU.TabletSensors";
    private static final float NIGHT_MODE_LOW = SensorManager.LIGHT_CLOUDY * 0.4f;
    private static final float NIGHT_MODE_HIGH = SensorManager.LIGHT_CLOUDY * 0.6f;
    private static final int CONSECUTIVE_CHANGES_TO_TRIGGER_DAY_NIGHT_MODE_CHANGE = 10;

    private final Display mDisplay;
    private final SensorManager mSensorManager;
    private final boolean mUseCompass;

    private final float[] mGravity = new float[3];
    private final float[] mMagneticField = new float[3];
    private final float[] mRotationSpeed = new float[3];
    private final float[] mRotationMatrix = new float[9];
    private final float[] mInclinationMatrix = new float[9];
    private final float[] mOrientation = new float[3];

    private int mAzimuth;
    private int mPitch = Integer.MIN_VALUE;
    private int mRoll = Integer.MIN_VALUE;

    private boolean mNightMode;
    private boolean mRecentNightMode;
    private int mDayNightModeCurrentModeAccumulated = 0;

    private float mTemperature = Float.NaN;
    private float mPressure = Float.NaN;
    private final boolean mUseSunCalculator;
    private final Context mContext;
    protected Location mLocation;
    private Handler mHandler;
    private TwilightCalculator mTc;

    public TabletSensors(Context context) {
        mContext = context;
        mDisplay = ((WindowManager) context.getSystemService(Context.WINDOW_SERVICE))
                .getDefaultDisplay();
        mSensorManager = (SensorManager) context.getSystemService(Context.SENSOR_SERVICE);

        Settings settings = new Settings(context);
        mUseCompass = settings.isUseTabletCompassEnabled();
        mUseSunCalculator = settings.useSunCalculator();
    }

    @Override
    protected void discoverSensorsImpl(Set<Integer> sensors) {
        Sensor lightSensor = mSensorManager.getDefaultSensor(Sensor.TYPE_LIGHT);
        Sensor accelerometer = mSensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER);
        Sensor magneticSensor = mSensorManager.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD);
        Sensor gyroscope = mSensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE);
        Sensor temperature = mSensorManager.getDefaultSensor(Sensor.TYPE_AMBIENT_TEMPERATURE);
        Sensor pressure = mSensorManager.getDefaultSensor(Sensor.TYPE_PRESSURE);

        if (lightSensor != null) {
            sensors.add(SensorSource.SENSOR_TYPE_NIGHT_MODE);
        }

        if (accelerometer != null) {
            sensors.add(SensorSource.SENSOR_TYPE_ACCELEROMETER_DATA);
        }

        if (accelerometer != null && magneticSensor != null && mUseCompass) {
            sensors.add(SensorSource.SENSOR_TYPE_COMPASS);
        }

        if (gyroscope != null) {
            sensors.add(SensorSource.SENSOR_TYPE_GYROSCOPE_DATA);
        }

        if (temperature != null || pressure != null) {
            sensors.add(SensorSource.SENSOR_TYPE_ENVIRONMENT);
        }
    }

  @Override
  //@UsesPermission(Permissions.ACCESS_FINE_LOCATION)
  protected void registerSensorsImpl() {
        if (isHandled(SensorSource.SENSOR_TYPE_NIGHT_MODE)) {
            if (mUseSunCalculator) {
                LocationManager locationManager = (LocationManager) mContext.getSystemService(
                        Context.LOCATION_SERVICE);
                mLocation = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
                if (mLocation == null) {
                    Log.i(TAG, "Unable to get location, using 120W/34N");
                    mLocation = new Location("fake");
                    // Somewhere in California.
                    mLocation.setLatitude(34);
                    mLocation.setLongitude(-120);
                }
                mTc = new TwilightCalculator();
                mHandler = new Handler(mContext.getMainLooper());
                mHandler.post(new SunCalculatorRunnable());
            } else {
                mSensorManager.registerListener(mSensorEventListener,
                        mSensorManager.getDefaultSensor(Sensor.TYPE_LIGHT),
                        SensorManager.SENSOR_DELAY_NORMAL);
            }
        }

        if (isHandled(SensorSource.SENSOR_TYPE_ACCELEROMETER_DATA)) {
            mSensorManager.registerListener(mSensorEventListener,
                    mSensorManager.getDefaultSensor(Sensor.TYPE_ACCELEROMETER),
                    SensorManager.SENSOR_DELAY_NORMAL);
        }

        if (isHandled(SensorSource.SENSOR_TYPE_COMPASS)) {
            mSensorManager.registerListener(mSensorEventListener,
                    mSensorManager.getDefaultSensor(Sensor.TYPE_MAGNETIC_FIELD),
                    SensorManager.SENSOR_DELAY_NORMAL);
        }

        if (isHandled(SensorSource.SENSOR_TYPE_GYROSCOPE_DATA)) {
            mSensorManager.registerListener(mSensorEventListener,
                    mSensorManager.getDefaultSensor(Sensor.TYPE_GYROSCOPE),
                    SensorManager.SENSOR_DELAY_NORMAL);
        }

        if (isHandled(SensorSource.SENSOR_TYPE_ENVIRONMENT)) {
            Sensor temperature = mSensorManager.getDefaultSensor(Sensor.TYPE_AMBIENT_TEMPERATURE);
            Sensor pressure = mSensorManager.getDefaultSensor(Sensor.TYPE_PRESSURE);
            if (temperature != null) {
                mSensorManager.registerListener(mSensorEventListener, temperature,
                        SensorManager.SENSOR_DELAY_NORMAL);
            }
            if (pressure != null) {
                mSensorManager.registerListener(mSensorEventListener, pressure,
                        SensorManager.SENSOR_DELAY_NORMAL);
            }
        }
    }

    @Override
    protected void unregisterSensorsImpl() {
        mSensorManager.unregisterListener(mSensorEventListener);
    }

    @Override
    protected boolean sendUpdateImpl(SensorSinkWrapper sink, int sensorType) {
        if (sensorType == SensorSource.SENSOR_TYPE_COMPASS) {
            sink.reportCompassData(mAzimuth, mPitch != Integer.MIN_VALUE, mPitch,
                    mRoll != Integer.MIN_VALUE, mRoll);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_ACCELEROMETER_DATA) {
            sink.reportAccelerometerData(true, mGravity[0], true, mGravity[1],
                    true, mGravity[2]);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_GYROSCOPE_DATA) {
            sink.reportGyroscopeData(true, mRotationSpeed[0], true, mRotationSpeed[1],
                    true, mRotationSpeed[2]);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_NIGHT_MODE) {
            sink.reportNightModeData(mNightMode);
            return true;
        } else if (sensorType == SensorSource.SENSOR_TYPE_ENVIRONMENT) {
            sink.reportEnvironmentData(mTemperature, mPressure);
            return true;
        }
        throw new RuntimeException();
    }

    private static float getVectorNorm(float... values) {
        float sum = 0;
        for (float value : values) {
            sum += (value * value);
        }
        return (float) Math.sqrt(sum);
    }

    private void updateCompass() {
        float norm = getVectorNorm(mGravity);
        if (norm > SensorManager.GRAVITY_EARTH * 1.1
                || norm < SensorManager.GRAVITY_EARTH * 0.9) {
            return;
        }
        if (SensorManager.getRotationMatrix(
                mRotationMatrix, mInclinationMatrix, mGravity, mMagneticField)) {
            SensorManager.getOrientation(mRotationMatrix, mOrientation);
            // Fill 3D compass first.
            int azimuth = (int) Math.round(Math.toDegrees(mOrientation[0]));
            int pitch = (int) Math.round(Math.toDegrees(mOrientation[1]));
            int roll = (int) Math.round(Math.toDegrees(mOrientation[2]));

            // Determine car's orientation assuming one of two landscape tablet orientations.
            boolean isReverseLandscape = mDisplay.getRotation() == Surface.ROTATION_270;
            boolean isFlat = Math.abs(roll) < 30;

            if (isReverseLandscape) {
                azimuth -= 90;
            } else {
                azimuth += 90;
            }

            int pitchOffset = isFlat ? 0 : 90;
            if (isReverseLandscape) {
                pitchOffset -= roll;
                roll = pitch;
            } else {
                pitchOffset += roll;
                roll = -pitch;
            }
            pitch = pitchOffset;

            // Normalize azimuth.
            azimuth = (azimuth % 360 + 360) % 360;

            mAzimuth = azimuth;
            mPitch = pitch;
            mRoll = roll;
        }
    }

    private final SensorEventListener mSensorEventListener = new SensorEventListener() {
        @Override
        public void onSensorChanged(SensorEvent event) {
            int type = event.sensor.getType();
            switch (type) {
                case Sensor.TYPE_LIGHT: {
                    float light = event.values[0];

                    // Different boundaries to avoid frequent changes on the borderline.
                    if (light > NIGHT_MODE_HIGH) {
                        if (mRecentNightMode) {
                            mDayNightModeCurrentModeAccumulated = 0;
                            mRecentNightMode = false;
                        } else {
                            mDayNightModeCurrentModeAccumulated++;
                        }
                    } else if (light < NIGHT_MODE_LOW) {
                        if (!mRecentNightMode) {
                            mDayNightModeCurrentModeAccumulated = 0;
                            mRecentNightMode = true;
                        } else {
                            mDayNightModeCurrentModeAccumulated++;
                        }
                    }
                    if (mRecentNightMode != mNightMode && mDayNightModeCurrentModeAccumulated >
                        CONSECUTIVE_CHANGES_TO_TRIGGER_DAY_NIGHT_MODE_CHANGE) {
                        mNightMode = mRecentNightMode;
                        reportAvailable(SensorSource.SENSOR_TYPE_NIGHT_MODE);
                    }
                    break;
                }
                case Sensor.TYPE_ACCELEROMETER:
                    mGravity[0] = event.values[0];
                    mGravity[1] = event.values[1];
                    mGravity[2] = event.values[2];
                    updateCompass();
                    // This sensor is updated with fixed frequency 10 Hz.
                    break;
                case Sensor.TYPE_MAGNETIC_FIELD:
                    mMagneticField[0] = event.values[0];
                    mMagneticField[1] = event.values[1];
                    mMagneticField[2] = event.values[2];
                    updateCompass();
                    // This sensor is updated with fixed frequency 10 Hz.
                    break;
                case Sensor.TYPE_GYROSCOPE:
                    mRotationSpeed[0] = event.values[0];
                    mRotationSpeed[1] = event.values[1];
                    mRotationSpeed[2] = event.values[2];
                    // This sensor is updated with fixed frequency 10 Hz.
                    break;
                case Sensor.TYPE_AMBIENT_TEMPERATURE:
                    mTemperature = event.values[0];
                    // This sensor is updated with fixed frequency 0.1 Hz.
                    break;
                case Sensor.TYPE_PRESSURE:
                    mPressure = event.values[0];
                    // This sensor is updated with fixed frequency 0.1 Hz.
                    break;
            }
        }

        @Override
        public void onAccuracyChanged(Sensor sensor, int accuracy) {
        }
    };

    private class SunCalculatorRunnable implements Runnable {
        @Override
        public void run() {
            long currentTimeMillis = System.currentTimeMillis();
            mTc.calculateTwilight(
                    currentTimeMillis, mLocation.getLatitude(), mLocation.getLongitude());
            mNightMode = mTc.mState == TwilightCalculator.NIGHT;
            reportAvailable(SensorSource.SENSOR_TYPE_NIGHT_MODE);
            long delayMillis =
                    ((mNightMode ? mTc.mSunrise : mTc.mSunset) - currentTimeMillis);
            Log.i(TAG, "Day/Night changing in "
                    + delayMillis / DateUtils.MINUTE_IN_MILLIS + " minutes");
            // Add a minute to make sure twilight calculator will report the changed mode.
            mHandler.postDelayed(this, delayMillis + DateUtils.MINUTE_IN_MILLIS);
        }
    }
}

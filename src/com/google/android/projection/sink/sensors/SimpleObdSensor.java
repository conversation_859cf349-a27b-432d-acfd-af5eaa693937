package com.google.android.projection.sink.sensors;

import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothSocket;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.text.TextUtils;
import android.util.Log;
import com.google.android.projection.protocol.SensorSource;
import com.google.android.projection.sink.settings.Settings;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.Set;
import java.util.UUID;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Sensor that connects to a Bluetooth OBD2 and gets data from there (wheelspeed, fuel).
 *
 * <p>Assumes ELM327 compatible Bluetooth dongle.
 */
public class SimpleObdSensor extends SensorCollectorBase {
    private static final String TAG = SimpleObdSensor.class.getSimpleName();
    private static final boolean DBG = false;
    private static final int RESPONSE_TO_01 = 0x41;

    /**
     * A "well-known" SPP UUID (see
     * http://developer.android.com/reference/android/bluetooth/BluetoothDevice.html).
     */
    private static final UUID SELF_UUID = UUID.fromString("00001101-0000-1000-8000-00805F9B34FB");
    private static final long OBD_REFRESH_PERIOD_MS = 1000;
    private static final float KPH_PER_MPS = 3.6f;

    private final boolean mEnabled;
    private final String mDeviceName;
    private BluetoothSocket mSocket;
    private Handler mHandler;
    private final CountDownLatch mHandlerInitLatch = new CountDownLatch(1);
    private final CountDownLatch mDiscoveryLatch = new CountDownLatch(1);
    private final Object mDataLock = new Object();
    private boolean mHasSpeedSensor;
    private boolean mHasRpmSensor;
    private boolean mHasFuelSensor;
    private int mSpeedKph;
    private int mRpm;
    private float mFuelLevel = Float.NaN;

    private InputStream mInput;
    private OutputStream mOutput;

    public SimpleObdSensor(Context context) {
        Settings settings = new Settings(context);
        mEnabled = settings.isObd2Enabled();
        mDeviceName = settings.getObd2Device();
        if (mEnabled && !TextUtils.isEmpty(mDeviceName)) {
            mObdThread.start();
            try {
                mHandlerInitLatch.await();
            } catch (InterruptedException e) {
                Log.e(TAG, "Can't start OBD thread.", e);
                throw new RuntimeException(e);
            }
        }
    }

    @Override
    public void shutdown() {
        if (mEnabled && mHandler != null) {
            mHandler.removeCallbacksAndMessages(null);
            mHandler.getLooper().quit();
            disconnect();
        }
        super.shutdown();
    }

    @Override
    protected void discoverSensorsImpl(Set<Integer> sensors) {
        mSocket = null;
        if (mEnabled && !TextUtils.isEmpty(mDeviceName)) {
            BluetoothAdapter adapter = BluetoothAdapter.getDefaultAdapter();
            BluetoothDevice device = adapter.getRemoteDevice(mDeviceName);
            try {
                mSocket = device.createRfcommSocketToServiceRecord(SELF_UUID);
                mHandler.post(mInitLink);
            } catch (IOException e) {
                Log.e(TAG, "Can't start bluetooth connection.", e);
            }
        }
        if (mSocket != null) {
            try {
                Log.d(TAG, "Waiting for sensors discovery...");
                if (mDiscoveryLatch.await(15, TimeUnit.SECONDS)) {
                    synchronized (mDataLock) {
                        if (mHasSpeedSensor) {
                            sensors.add(SensorSource.SENSOR_TYPE_SPEED);
                        }
                        if (mHasRpmSensor) {
                            sensors.add(SensorSource.SENSOR_TYPE_RPM);
                        }
                        if (mHasFuelSensor) {
                            sensors.add(SensorSource.SENSOR_TYPE_FUEL);
                        }
                    }
                } else {
                    Log.w(TAG, "Timed out while discovering OBD2 sensors.");
                }
            } catch (InterruptedException e) {
                Log.e(TAG, "Can't discover OBD sensors.", e);
            }
        }
    }

    @Override
    protected void registerSensorsImpl() {
    }

    @Override
    protected void unregisterSensorsImpl() {
        if (mHandler != null) {
            mHandler.post(mDiconnect);
        }
    }

    @Override
    protected boolean sendUpdateImpl(SensorSinkWrapper sink, int sensorType) {
        synchronized (mDataLock) {
            if (sensorType == SensorSource.SENSOR_TYPE_SPEED) {
                sink.reportSpeedData((int) (mSpeedKph / KPH_PER_MPS));
                return true;
            } else if (sensorType == SensorSource.SENSOR_TYPE_RPM) {
                sink.reportRpmData(mRpm);
                return true;
            } else if (sensorType == SensorSource.SENSOR_TYPE_FUEL) {
                sink.reportFuelData((int) (mFuelLevel * 100), 0, mFuelLevel <= 0.2f);
                return true;
            }
        }
        throw new RuntimeException();
    }

    private static void sendCommand(OutputStream out, String command) throws IOException {
        if (DBG) {
            Log.d(TAG, String.format("wr '%s'", command));
        }
        String data = command + "\r";
        out.write(data.getBytes());
        out.flush();
    }

    private static String readResponse(InputStream in) throws IOException {
        char c;
        StringBuilder builder = new StringBuilder();
        while ((c = (char) (byte) in.read()) != '>') {
            builder.append(c);
        }
        String fullData = builder.toString().trim().replaceAll("\r", "\n");
        if (DBG) {
            Log.d(TAG, String.format("rd '%s'", fullData));
        }
        String lastLine = fullData.substring(fullData.lastIndexOf('\n') + 1);
        return lastLine;
    }

    /**
     * Returns data (can be empty) from the last line of the last response or null if error ocured.
     */
    private static byte[] parseResponse(String response) {
        String message = response.replaceAll(" ", "");

        if (DBG) {
            Log.d(TAG, String.format("hx '%s'", message));
        }

        if (message.contains("DATA") || message.contains("SEARCH") || message.contains("CONNECT")) {
            return null;
        }

        if (message.equals("OK")) {
            return new byte[0];
        }

        int length = message.length();
        if (length % 2 != 0) {
            throw new RuntimeException("protocol error");
        }

        byte[] data = new byte[length / 2];
        for (int i = 0; i < length; i += 2) {
            data[i / 2] = (byte) ((Character.digit(message.charAt(i), 16) << 4)
                                 + Character.digit(message.charAt(i + 1), 16));
        }
        return data;
    }

    private boolean querySpeed() throws IOException {
        // Query speed.
        sendCommand(mOutput, "01 0D");
        byte[] data = parseResponse(readResponse(mInput));
        if (data != null && data.length >= 3 && data[0] == RESPONSE_TO_01 && data[1] == 0x0D) {
            mSpeedKph = (0xFF & data[2]);
            return true;
        }
        return false;
    }

    private boolean queryRpm() throws IOException {
        // Query RPM.
        sendCommand(mOutput, "01 0C");
        byte[] data = parseResponse(readResponse(mInput));
        if (data != null && data.length >= 4 && data[0] == RESPONSE_TO_01 && data[1] == 0x0C) {
      mRpm = (((data[2] << 8) | (data[3] & 0xFF)) & 0xFFFF) >> 2;
            return true;
        }
        return false;
    }

    private boolean queryFuel() throws IOException {
        // Query fuel level.
        sendCommand(mOutput, "01 2F");
        byte[] data = parseResponse(readResponse(mInput));
        if (data != null && data.length >= 3 && data[0] == RESPONSE_TO_01 && data[1] == 0x2F) {
            mFuelLevel = (0xFF & data[2]) / 256f;
            return true;
        }
        return false;
    }

    private void disconnect() {
        if (mInput != null) {
            try {
                mInput.close();
            } catch (IOException e) {
                // ignore
            }
            mInput = null;
        }
        if (mOutput != null) {
            try {
                mOutput.close();
            } catch (IOException e) {
                // ignore
            }
            mOutput = null;
        }
        if (mSocket != null) {
            try {
                mSocket.close();
            } catch (IOException e) {
                // ignore
            }
            mSocket = null;
        }
    }

    private final Thread mObdThread = new Thread("OBD connection") {
        @Override
        public void run() {
            Looper.prepare();
            mHandler = new Handler();
            mHandlerInitLatch.countDown();
            Looper.loop();
        }
    };

    private final Runnable mInitLink = new Runnable() {
        @Override
        public void run() {
            try {
                mSocket.connect();
                mInput = mSocket.getInputStream();
                mOutput = mSocket.getOutputStream();
                Log.d(TAG, "Connected to OBD");

                // Reset OBD2
                sendCommand(mOutput, "AT Z");
                readResponse(mInput);
                // Echo off.
                sendCommand(mOutput, "AT E0");
                readResponse(mInput);
                // Line feed off.
                sendCommand(mOutput, "AT L0");
                readResponse(mInput);
                // Set timeout to 50.
                sendCommand(mOutput, "AT ST " + Integer.toHexString(50));
                readResponse(mInput);
                // Set protocol to auto.
                sendCommand(mOutput, "AT SP 0");
                readResponse(mInput);

                // Query sensors.
                synchronized (mDataLock) {
                    mHasSpeedSensor = querySpeed();
                    mHasRpmSensor = queryRpm();
                    mHasFuelSensor = queryFuel();
                    Log.d(TAG, "Sensor discovery completed");
                    mDiscoveryLatch.countDown();
                }

                mHandler.postDelayed(mRegularUpdate, OBD_REFRESH_PERIOD_MS);
              } catch (IOException e) {
                Log.e(TAG, "Can't connect", e);
              }
        }
    };

    private final Runnable mRegularUpdate = new Runnable() {
        @Override
        public void run() {
            try {
                synchronized (mDataLock) {
                    if (mHasSpeedSensor) {
                        querySpeed();
                    }
                    if (mHasRpmSensor) {
                        queryRpm();
                    }
                    if (mHasFuelSensor) {
                        queryFuel();
                    }
                }
                mHandler.postDelayed(this, OBD_REFRESH_PERIOD_MS);
            } catch (IOException e) {
                Log.e(TAG, "Disconnect.", e);
            }
        }
    };

    private final Runnable mDiconnect = new Runnable() {
        @Override
        public void run() {
            disconnect();
        }
    };
}

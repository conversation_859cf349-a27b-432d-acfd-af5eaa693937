package com.google.android.projection.sink.sensors;

import android.location.Location;
import com.google.android.projection.protocol.SensorSource;

/**
 * Allows overriding sending methods to intercept data.
 */
public class SensorSinkWrapper {
    private final SensorSource mSensors;
    public SensorSinkWrapper(SensorSource sensors) {
        mSensors = sensors;
    }

    public void reportLocation(Location location) {
        mSensors.reportLocation(location);
    }

    public void reportCompassData(
            int bearing, boolean hasPitch, int pitch, boolean hasRoll, int roll) {
        mSensors.reportCompassData(bearing, hasPitch, pitch, hasRoll, roll);
    }

    public void reportSpeedData(float speed) {
        mSensors.reportSpeedData(speed, /* hasCruiseEnaged */ false, /* cruiseEngaged */ false,
                /* hasCruiseSetSpeed */ false, /* cruiseSetSpeed */ 0);
    }

    public void reportRpmData(double rpm) {
        mSensors.reportRpmData(rpm);
    }

    public void reportOdometerData(double kms) {
        mSensors.reportOdometerData(kms, /* hasTripKms */ false, /* tripKms */ 0);
    }

    public void reportFuelData(int fuelRemainingPercent, int rangeKm, boolean lowLevelWarning) {
        mSensors.reportFuelData(/* hasFuelRemainingPercent */ true, fuelRemainingPercent,
                /* hasRangeKms */ true, rangeKm, /* hasLowLevelWarning */ true, lowLevelWarning);
    }

    public void reportParkingBrakeData(boolean engaged) {
        mSensors.reportParkingBrakeData(engaged);
    }

    public void reportGearData(int gear) {
        mSensors.reportGearData(gear);
    }

    public void reportDiagnosticsData(byte[] data) {
        mSensors.reportDiagnosticsData(data);
    }

    public void reportNightModeData(boolean nightMode) {
        mSensors.reportNightModeData(nightMode);
    }

    public void reportEnvironmentData(float temperature, float pressure) {
        mSensors.reportEnvironmentData(/* hasTemperature */ true, temperature,
                /* hasPressure */ true, pressure, /* hasRain */ false, /* rain */ 0);
    }

    public void reportHvacData(float targetTemperature, float currentTemperature) {
        mSensors.reportHvacData(/* hasTargetTemperature */ true, targetTemperature,
                /* hasCurrentTemperature */ true ,currentTemperature);
    }

    public void reportDrivingStatusData(int drivingStatus) {
        mSensors.reportDrivingStatusData(drivingStatus);
    }

    public void reportAccelerometerData(boolean hasAccelerationX, float accelerationX,
            boolean hasAccelerationY, float accelerationY, boolean hasAccelerationZ,
            float accelerationZ) {
        mSensors.reportAccelerometerData(hasAccelerationX, accelerationX,
                hasAccelerationY, accelerationY, hasAccelerationZ, accelerationZ);
    }

    public void reportGyroscopeData(boolean hasRotationSpeedX, float rotationSpeedX,
            boolean hasRotationSpeedY, float rotationSpeedY, boolean hasRotationSpeedZ,
            float rotationSpeedZ) {
        mSensors.reportGyroscopeData(hasRotationSpeedX, rotationSpeedX,
                hasRotationSpeedY, rotationSpeedY, hasRotationSpeedZ, rotationSpeedZ);
    }

    public void reportGpsSatelliteData(int numberInUse, boolean hasNumberInView,
            int numberInView, int[] prns, float[] snrs, boolean[] usedInFix, float[] azimuths,
            float[] elevations) {
        mSensors.reportGpsSatelliteData(numberInUse, hasNumberInView, numberInView,
                prns, snrs, usedInFix, azimuths, elevations);
    }
}

// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.settings;

import static com.google.android.projection.sink.settings.Settings.CAR_BLUETOOTH_ADDRESS;
import static com.google.android.projection.sink.settings.Settings.CAR_WIFI_PASSWORD;
import static com.google.android.projection.sink.settings.Settings.CAR_WIFI_SECURITY;
import static com.google.android.projection.sink.settings.Settings.CAR_WIFI_SSID;
import static com.google.android.projection.sink.settings.Settings.LISTEN_TCP_PORT;
import static com.google.android.projection.sink.settings.Settings.LOCATION_SOURCE;
import static com.google.android.projection.sink.settings.Settings.OBD2_DEVICE;
import static com.google.android.projection.sink.settings.Settings.OBD2_ENABLED;
import static com.google.android.projection.sink.settings.Settings.SCREEN_DPI;
import static com.google.android.projection.sink.settings.Settings.SCREEN_HEIGHT;
import static com.google.android.projection.sink.settings.Settings.SCREEN_SCALE;
import static com.google.android.projection.sink.settings.Settings.SCREEN_WIDTH;

import android.Manifest;
import android.app.ActivityManager;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.content.Intent;
import android.content.SharedPreferences;
import android.content.SharedPreferences.Editor;
import android.content.pm.PackageManager;
import android.os.Bundle;
import android.preference.CheckBoxPreference;
import android.preference.ListPreference;
import android.preference.Preference;
import android.preference.PreferenceActivity;
import android.preference.PreferenceManager;
import android.preference.PreferenceScreen;
import android.widget.Toast;

import androidx.core.app.ActivityCompat;

import com.google.android.projection.sink.R;

import java.util.ArrayList;

/**
 * Demo presentations settings
 */
public class SettingsActivity extends PreferenceActivity
        implements Preference.OnPreferenceChangeListener {

    BluetoothAdapter mBluetoothAdapter;

    @Override
    protected void onPostCreate(Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);
        if (ActivityManager.isUserAMonkey()) {
            finish();
        }

        setupSimplePreferencesScreen();
    }

    /**
     * Shows the simplified settings UI if the device configuration if the
     * device configuration dictates that a simplified, single-pane UI should be
     * shown.
     */
    @SuppressWarnings("deprecation") // Non-fragments API
    private void setupSimplePreferencesScreen() {
        // In the simplified UI, fragments are not used at all and we instead
        // use the older PreferenceActivity APIs.

        // Add 'general' preferences.
        addPreferencesFromResource(R.xml.pref_general);

        // Bind the summaries of EditText/List/etc. preferences to
        // their values. When their values change, their summaries are updated
        // to reflect the new value, per the Android Design guidelines.
        SettingsUtils.bindPreferenceSummaryToValue(findPreference("preset_list"),
                new Preference.OnPreferenceChangeListener() {
                    @SuppressWarnings("deprecation") // Non-fragments API
                    @Override
                    public boolean onPreferenceChange(Preference preference, Object newValue) {
                        handlePresetChange(getPreferenceScreen(), newValue);
                        return SettingsActivity.this.onPreferenceChange(preference, newValue);
                    }
                });
        SettingsUtils.bindPreferenceSummaryToValue(findPreference(SCREEN_WIDTH), this);
        SettingsUtils.bindPreferenceSummaryToValue(findPreference(SCREEN_HEIGHT), this);
        SettingsUtils.bindPreferenceSummaryToValue(findPreference(SCREEN_DPI), this);
        SettingsUtils.bindPreferenceSummaryToValue(findPreference(LISTEN_TCP_PORT), this);
        SettingsUtils.bindPreferenceSummaryToValue(findPreference(CAR_WIFI_SSID), this);
        SettingsUtils.bindPreferenceSummaryToValue(findPreference(CAR_WIFI_PASSWORD), this);
        SettingsUtils.bindPreferenceSummaryToValue(findPreference(CAR_BLUETOOTH_ADDRESS), this);
        Preference simulationPreference = findPreference(LOCATION_SOURCE);
        SettingsUtils.bindPreferenceSummaryToValue(simulationPreference,
                new Preference.OnPreferenceChangeListener() {
                    @Override
                    public boolean onPreferenceChange(Preference preference, Object newValue) {
                        handleGpsSourceChange(newValue);
                        return SettingsActivity.this.onPreferenceChange(preference, newValue);
                    }
                });
        Preference carWifiSecurityPreference = findPreference("wifi_security_list");
        SettingsUtils.bindPreferenceSummaryToValue(carWifiSecurityPreference,
                new Preference.OnPreferenceChangeListener() {
                    @Override
                    public boolean onPreferenceChange(Preference preference, Object newValue) {
                        handleCarWifiSecurityChange(getPreferenceScreen(), newValue);
                        return SettingsActivity.this.onPreferenceChange(preference, newValue);
                    }
                });

        mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();

        ListPreference obdDevicePreference = (ListPreference) findPreference(OBD2_DEVICE);
        CheckBoxPreference obdEnablePreference = (CheckBoxPreference) findPreference(OBD2_ENABLED);

        SettingsUtils.syncSummaryToValue(obdDevicePreference);
        obdDevicePreference.setOnPreferenceClickListener(
                new Preference.OnPreferenceClickListener() {
                    @Override
                    public boolean onPreferenceClick(Preference preference) {
                        return isBluetoothWorking();
                    }
                });
        if (!isBluetoothWorking()) {
            obdEnablePreference.setChecked(false);
            obdEnablePreference.setEnabled(false);
        } else {
            // Populate list with paired devices.
            ArrayList<CharSequence> pairedDeviceNames = new ArrayList<CharSequence>();
            ArrayList<CharSequence> pairedDeviceAddresses = new ArrayList<CharSequence>();
            if (ActivityCompat.checkSelfPermission(this, Manifest.permission.BLUETOOTH_CONNECT) != PackageManager.PERMISSION_GRANTED) {
                // TODO: Consider calling
                //    ActivityCompat#requestPermissions
                // here to request the missing permissions, and then overriding
                //   public void onRequestPermissionsResult(int requestCode, String[] permissions,
                //                                          int[] grantResults)
                // to handle the case where the user grants the permission. See the documentation
                // for ActivityCompat#requestPermissions for more details.
                return;
            }
            for (BluetoothDevice device : mBluetoothAdapter.getBondedDevices()) {
                pairedDeviceNames.add(device.getName());
                pairedDeviceAddresses.add(device.getAddress());
            }
            obdDevicePreference.setEntries(pairedDeviceNames.toArray(new CharSequence[0]));
            obdDevicePreference.setEntryValues(pairedDeviceAddresses.toArray(new CharSequence[0]));
        }
        SettingsUtils.bindPreferenceSummaryToValue(obdDevicePreference, this);
    }

    /**
     * A preference value change listener that updates the preference's summary
     * to reflect its new value.
     */
    @Override
    public boolean onPreferenceChange(Preference preference, Object value) {
        return SettingsUtils.updatePreferenceSummary(preference, value);
    }

    private void handlePresetChange(PreferenceScreen prefScreen, Object newValue) {
        int id = Integer.parseInt(newValue.toString());
        SharedPreferences prefs = PreferenceManager
                .getDefaultSharedPreferences(prefScreen.getContext());
        prefScreen.findPreference(SCREEN_WIDTH).setEnabled(id == 1);
        prefScreen.findPreference(SCREEN_HEIGHT).setEnabled(id == 1);
        prefScreen.findPreference(SCREEN_DPI).setEnabled(id == 1);
        prefScreen.findPreference(SCREEN_SCALE).setEnabled(id != 0);
        Editor editor = prefs.edit();
        if (id == 0) {
            // Native preset
            editor.putString(SCREEN_WIDTH, "0");
            editor.putString(SCREEN_HEIGHT, "0");
            editor.putString(SCREEN_DPI, "0");
        } else if (id == 1) {
            // Custom preset: reset to some meaningful values if they are
            // invalid
            if (Integer.parseInt(prefs.getString(SCREEN_WIDTH, "0")) == 0) {
                editor.putString(SCREEN_WIDTH, "800");
            }
            if (Integer.parseInt(prefs.getString(SCREEN_HEIGHT, "0")) == 0) {
                editor.putString(SCREEN_HEIGHT, "480");
            }
            if (Integer.parseInt(prefs.getString(SCREEN_DPI, "0")) == 0) {
                editor.putString(SCREEN_DPI, "160");
            }
        } else if (id == 2) {
            // F**d/H****i
            editor.putString(SCREEN_WIDTH, "800");
            editor.putString(SCREEN_HEIGHT, "480");
            editor.putString(SCREEN_DPI, "133");
        } else if (id == 3) {
            // T****a
            editor.putString(SCREEN_WIDTH, "800");
            editor.putString(SCREEN_HEIGHT, "480");
            editor.putString(SCREEN_DPI, "155");
        } else if (id == 4) {
            // "Reference" 160 DPI
            editor.putString(SCREEN_WIDTH, "800");
            editor.putString(SCREEN_HEIGHT, "480");
            editor.putString(SCREEN_DPI, "160");
        } else if (id == 5) {
            // Nexus 7 7" @ 1280x720
            editor.putString(SCREEN_WIDTH, "1280");
            editor.putString(SCREEN_HEIGHT, "720");
            editor.putString(SCREEN_DPI, "215");
        } else {
            throw new RuntimeException("Unknown preset");
        }
        editor.commit();
        onPreferenceChange(prefScreen.findPreference(SCREEN_WIDTH),
                prefs.getString(SCREEN_WIDTH, "0"));
        onPreferenceChange(prefScreen.findPreference(SCREEN_HEIGHT),
                prefs.getString(SCREEN_HEIGHT, "0"));
        onPreferenceChange(prefScreen.findPreference(SCREEN_DPI),
                prefs.getString(SCREEN_DPI, "0"));
    }

    private void handleGpsSourceChange(Object newValue) {
        String value = newValue.toString();
        if (value.equals(Settings.GPS_SIMULATION)) {
            // Need to specify start and end point for emulation
            startActivity(new Intent(this, SimulationSettingsActivity.class));
        }
    }

    private void handleCarWifiSecurityChange(PreferenceScreen prefScreen, Object newValue) {
        int securityType = Integer.parseInt(newValue.toString());
        SharedPreferences prefs = PreferenceManager
                .getDefaultSharedPreferences(prefScreen.getContext());
        Editor editor = prefs.edit();
        editor.putInt(CAR_WIFI_SECURITY, securityType);
        editor.commit();
    }

    private boolean isBluetoothWorking() {
        if (mBluetoothAdapter == null || !mBluetoothAdapter.isEnabled()) {
          Toast.makeText(this, getString(R.string.no_bluetooth), Toast.LENGTH_SHORT).show();
          return false;
        }
        return true;
      }
}

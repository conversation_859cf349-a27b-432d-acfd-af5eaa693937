// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.settings;

import android.app.ProgressDialog;
import android.content.Context;
import android.content.SharedPreferences;
import android.location.Location;
import android.location.LocationManager;
import android.os.AsyncTask;
import android.os.Bundle;
import android.preference.Preference;
import android.preference.PreferenceActivity;
import android.preference.PreferenceManager;
import android.util.Log;
import android.widget.Toast;
//import com.google.android.gms.annotation.Permissions;
//import com.google.android.gms.annotation.UsesPermission;
import com.google.android.projection.protocol.AndroidHeadUnitLogging;
import com.google.android.projection.sink.R;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Simulation settings
 */
public class SimulationSettingsActivity extends PreferenceActivity
        implements Preference.OnPreferenceChangeListener {
    private static final String TAG = AndroidHeadUnitLogging.TAG_SETTINGS;
    private static final String START = "simulation_start";
    private static final String FINISH = "simulation_end";
    private static final String PREDEFINED_START_ADDRESS =
            "1600 Amphitheatre Parkway, Mountain View, CA 94043, USA";
    private static final String PREDEFINED_FINISH_ADDRESS =
            "604 Coronado Avenue, Stanford, CA 94305, USA";

    @Override
    protected void onPostCreate(Bundle savedInstanceState) {
        super.onPostCreate(savedInstanceState);

        setupSimplePreferencesScreen();
    }

    @SuppressWarnings("deprecation") // Non-fragments API
    private void setupSimplePreferencesScreen() {
        // In the simplified UI, fragments are not used at all and we instead
        // use the older PreferenceActivity APIs.

        // Add 'general' preferences.
        addPreferencesFromResource(R.xml.pref_simulation);

        // Set some initial addresses if current are empty.
        Preference startPreference = findPreference(START);
        Preference finishPreference = findPreference(FINISH);
        SharedPreferences prefs = PreferenceManager
                .getDefaultSharedPreferences(startPreference.getContext());
        String start = prefs.getString(START, "");
        String finish = prefs.getString(FINISH, "");
        if (start.isEmpty()) {
            startPreference.getEditor().putString(START, PREDEFINED_START_ADDRESS).commit();
        }
        if (finish.isEmpty()) {
            finishPreference.getEditor().putString(FINISH, PREDEFINED_FINISH_ADDRESS).commit();
        }

        // Bind the summaries of EditText/List/etc. preferences to
        // their values. When their values change, their summaries are updated
        // to reflect the new value, per the Android Design guidelines.
        SettingsUtils.bindPreferenceSummaryToValue(startPreference, this);
        SettingsUtils.bindPreferenceSummaryToValue(finishPreference, this);
    }

    /**
     * A preference value change listener that updates the preference's summary
     * to reflect its new value.
     */
    @Override
    public boolean onPreferenceChange(Preference preference, Object value) {
        validateAddress(preference, value.toString());
        return SettingsUtils.updatePreferenceSummary(preference, value);
    }

  //@UsesPermission(Permissions.ACCESS_FINE_LOCATION)
  private void validateAddress(final Preference preference, String address) {
        Log.d(TAG, "validateAddress(" + address + ")");
        LocationManager locationManager =
                (LocationManager) this.getSystemService(Context.LOCATION_SERVICE);
        Location lastLocation =
                locationManager.getLastKnownLocation(LocationManager.NETWORK_PROVIDER);
        if (lastLocation == null) {
            lastLocation = locationManager.getLastKnownLocation(LocationManager.GPS_PROVIDER);
        }
        QueryAddressTask queryAddressTask = new QueryAddressTask(preference, lastLocation);
        queryAddressTask.execute(address);
    }

    private static String getBounds(Location location) {
        return String.format("%.02f,%.02f|%.02f,%.02f",
                location.getLatitude() - 2, location.getLongitude() - 2,
                location.getLatitude() + 2, location.getLongitude() + 2);
    }

    private static URL getGeocodeUrl(String address, Location location) {
        try {
            return new URL("http://maps.googleapis.com/maps/api/geocode/json"
                    + "?address=" + URLEncoder.encode(address, "UTF-8")
                    + (location == null ?
                            "" : "&bounds=" + URLEncoder.encode(getBounds(location), "UTF-8"))
                    + "&sensor=false");
        } catch (MalformedURLException e) {
            Log.e(TAG, "Exception", e);
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "Exception", e);
        }
        return null;
    }

    private static List<String> queryRefinedAddress(String address, Location location) {
        URL url = getGeocodeUrl(address, location);
        if (url == null) {
            return null;
        }
        try {
            URLConnection urlConnection = url.openConnection();
            urlConnection.setRequestProperty("User-Agent",
                    "Google Map Navigation Beta Demo Launcher");
            BufferedReader reader =
                    new BufferedReader(new InputStreamReader(urlConnection.getInputStream()));

      StringBuilder jsonString = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                jsonString.append(line);
            }
            JSONObject root = new JSONObject(jsonString.toString());
            JSONArray results = root.getJSONArray("results");
            List<String> addresses = new ArrayList<String>();
            for (int i = 0; i < results.length(); ++i) {
                addresses.add(results.getJSONObject(i).getString("formatted_address"));
            }
            return addresses;
        } catch (IOException exception) {
            Log.e(TAG, "Exception", exception);
            return null;
        } catch (JSONException exception) {
            Log.e(TAG, "Exception", exception);
            return null;
        }
    }

    private class QueryAddressTask extends AsyncTask<String, Void, String> {
        private final Preference preference;
        private final Location location;
        private ProgressDialog dialog;

        public QueryAddressTask(Preference preference, Location location) {
            this.preference = preference;
            this.location = location;
        }

        @Override
        protected void onPreExecute () {
            dialog = ProgressDialog.show(SimulationSettingsActivity.this,
                    getText(R.string.checking_address), getText(R.string.please_wait));
        }
        @Override
        protected String doInBackground(String... addresses) {
            String address = addresses[0];
            List<String> result = queryRefinedAddress(address, location);
            if (result == null || result.isEmpty()) {
                return null;
            }
            return result.get(0);
        }
        @Override
        protected void onPostExecute(String result) {
            dialog.dismiss();
            if (result != null) {
                preference.getEditor().putString(preference.getKey(), result).commit();
                preference.setSummary(result);
            } else {
                Toast.makeText(SimulationSettingsActivity.this,
                        getText(R.string.invalid_address), Toast.LENGTH_LONG).show();
            }
        }
    }
}

// Copyright 2015 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.settings;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.SharedPreferences;
import android.os.Bundle;
import android.preference.PreferenceManager;
import android.util.Log;

public class SettingsBroadcastReceiver extends BroadcastReceiver {
    private static final String TAG = "AHU.SETTINGS";

    @Override
    public void onReceive(Context context, Intent intent) {
        SharedPreferences prefs = PreferenceManager.getDefaultSharedPreferences(context);
        SharedPreferences.Editor editor = prefs.edit();
        Bundle extras = intent.getExtras();

        if (extras != null) {
            for (String key : extras.keySet()) {
                Object obj = extras.get(key);
                if (obj instanceof String) {
                    Log.i(TAG, "Setting " + key + " to [String]: " + obj);
                    editor.putString(key, (String) obj);
                } else if (obj instanceof Integer) {
                    Log.i(TAG, "Setting " + key + " to [Integer]: " + obj);
                    editor.putInt(key, (int) obj);
                } else if (obj instanceof Boolean) {
                    Log.i(TAG, "Setting " + key + " to [Boolean]: " + obj);
                    editor.putBoolean(key, (boolean) obj);
                }
            }

            editor.commit();
        }
    }
}

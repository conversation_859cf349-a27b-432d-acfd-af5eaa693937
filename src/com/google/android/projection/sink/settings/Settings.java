// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.settings;

import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;

import com.google.android.projection.proto.Protos.BluetoothPairingMethod;

/**
 * Class holding all settings.
 */
public class Settings {
    public static final String SCREEN_WIDTH = "screen_width";
    public static final String SCREEN_HEIGHT = "screen_height";
    public static final String SCREEN_DPI = "screen_dpi";
    public static final String SCREEN_SCALE = "scale_checkbox";
    public static final String LOCATION_SOURCE = "location_source_list";
    public static final String DEBUG_MODE = "debug_mode";
    public static final String SIMULATION_START = "simulation_start";
    public static final String SIMULATION_FINISH = "simulation_end";
    public static final String SIMULATION_MISSING_ACCURACY = "missing_accuracy";
    public static final String SIMULATION_MISSING_BEARING = "missing_bearing";
    public static final String SIMULATION_MISSING_SPEED = "missing_speed";
    public static final String LISTEN_TCP_PORT = "listen_tcp_port";
    public static final String CAR_WIFI_SSID = "car_wifi_ssid";
    public static final String CAR_WIFI_PASSWORD = "car_wifi_password";
    public static final String CAR_WIFI_SECURITY = "car_wifi_security";
    public static final String WIFI_AUTO_START = "wifi_auto_start";
    public static final String CAR_BLUETOOTH_ADDRESS = "car_bluetooth_address";
    public static final String CAR_SUPPORTS_BLUETOOTH_NUMERIC_COMPARISON =
            "car_supports_bluetooth_numeric_comparison";
    public static final String CAR_SUPPORTS_BLUETOOTH_PIN = "car_supports_bluetooth_pin";
    public static final String ROTARY_CONTROLLER_ENABLED = "rotary_controller_enabled";
    public static final String TOUCH_SCREEN_ENABLED = "touch_screen_enabled";
    public static final String TOUCHPAD_NAVIGATION_ENABLED = "touchpad_navigation_enabled";
    public static final String GENERATE_MISSING_SENSORS = "generate_missing_sensors";
    public static final String USE_TABLET_COMPASS = "use_tablet_compass_enabled";
    public static final String AUTO_START_PROJECTION = "auto_start_projection";

    public static final String PREF_KEY_FPS_30 = "framerate_restrict_to_30";
    public static final String PREF_KEY_USE_RESTRICTED_AUDIO_FOCUS_MODES =
            "audio_focus_use_restricted_mode";

    public static final String GPS_TABLET = "gps";
    public static final String GPS_SIMULATION = "simulation";

    public static final String OBD2_ENABLED = "use_obd2_enabled";
    public static final String OBD2_DEVICE = "obd2_device";
    public static final String AUTO_START_LOCAL_MODE = "auto_start_local_mode";

    public static final String AUDIO_AAC_ENABLED_MEDIA = "use_aac_for_media_stream";
    public static final String AUDIO_AAC_ENABLED_TTS = "use_aac_for_tts_stream";

    private static final String USE_SUN_CALCULATOR = "use_sun_calculator";
    private static final String VOLUME_TOGGLE_DEBUG = "volume_toggle_debug";
    private static final String SHOW_VIDEO_FOCUS_DIALOG = "show_vf_dialog";

    private final SharedPreferences mPreferences;

    public Settings(Context context) {
        mPreferences = PreferenceManager.getDefaultSharedPreferences(context);
    }

    public int getEmulatedWidth() {
        try {
            return Integer.parseInt(mPreferences.getString(SCREEN_WIDTH, "800"));
        } catch (NumberFormatException e) {
            return 800;
        }
    }

    public int getEmulatedHeight() {
        try {
            return Integer.parseInt(mPreferences.getString(SCREEN_HEIGHT, "480"));
        } catch (NumberFormatException e) {
            return 480;
        }
    }

    public float getEmulatedDpi() {
        try {
            return Integer.parseInt(mPreferences.getString(SCREEN_DPI, "160"));
        } catch (NumberFormatException e) {
            return 160;
        }
    }

    public boolean isScaledToScreen() {
        return mPreferences.getBoolean(SCREEN_SCALE, true);
    }

    private String getLocationSource() {
        return mPreferences.getString(LOCATION_SOURCE, GPS_TABLET);
    }

    public boolean isGpsTabletEnabled() {
        return GPS_TABLET.equals(getLocationSource());
    }

    public boolean isGpsSimulationEnabled() {
        return GPS_SIMULATION.equals(getLocationSource());
    }

    public boolean isDebugMode() {
        return mPreferences.getBoolean(DEBUG_MODE, false);
    }

    public String getSimulationStart() {
        return mPreferences.getString(SIMULATION_START, "");
    }

    public String getSimulationFinish() {
        return mPreferences.getString(SIMULATION_FINISH, "");
    }

    public boolean isSimulateMissingAccuracyEnabled() {
      return mPreferences.getBoolean(SIMULATION_MISSING_ACCURACY, false);
    }

    public boolean isSimulateMissingBearingEnabled() {
      return mPreferences.getBoolean(SIMULATION_MISSING_BEARING, false);
    }

    public boolean isSimulateMissingSpeedEnabled() {
      return mPreferences.getBoolean(SIMULATION_MISSING_SPEED, false);
    }

    public int getListenTcpPort() {
        return Integer.parseInt(mPreferences.getString(LISTEN_TCP_PORT, "0"));
    }

    public String getCarWifiSsid() {
        return mPreferences.getString(CAR_WIFI_SSID, "");
    }

    public String getCarWifiPassword() {
        return mPreferences.getString(CAR_WIFI_PASSWORD, "");
    }

    public int getCarWifiSecurity() {
        return mPreferences.getInt(CAR_WIFI_SECURITY, 0);
    }

    public boolean getWifiAutoStart() {
        return mPreferences.getBoolean(WIFI_AUTO_START, true);
    }


    public String getCarBluetoothAddress() {
        // "SKIP_THIS_BLUETOOTH" is a magic fake Bluetooth MAC address that skips pairing.
        return mPreferences.getString(CAR_BLUETOOTH_ADDRESS, "SKIP_THIS_BLUETOOTH");
    }

    public boolean isBluetoothNumericComparisonSupported() {
        return mPreferences.getBoolean(CAR_SUPPORTS_BLUETOOTH_NUMERIC_COMPARISON, true);
    }

    public boolean isBluetoothPinSupported() {
        return mPreferences.getBoolean(CAR_SUPPORTS_BLUETOOTH_PIN, true);
    }

    public int[] getSupportedBluetoothPairingMethods() {
        if (isBluetoothNumericComparisonSupported()) {
            if (isBluetoothPinSupported()) {
                return new int[] {
                  BluetoothPairingMethod.BLUETOOTH_PAIRING_NUMERIC_COMPARISON.getNumber(),
                  BluetoothPairingMethod.BLUETOOTH_PAIRING_PIN.getNumber()
                };
            } else {
                return new int[] {
                  BluetoothPairingMethod.BLUETOOTH_PAIRING_NUMERIC_COMPARISON.getNumber()
                };
            }
        } else {
            if (isBluetoothPinSupported()) {
                return new int[] {
                  BluetoothPairingMethod.BLUETOOTH_PAIRING_PIN.getNumber()
                };
            } else {
                return new int[] {};
            }
        }
    }

    public boolean isRotaryControllerEnabled() {
        return mPreferences.getBoolean(ROTARY_CONTROLLER_ENABLED, false);
    }

    public boolean isTouchScreenEnabled() {
        return mPreferences.getBoolean(TOUCH_SCREEN_ENABLED, true);
    }

    public boolean isTouchpadNavigationEnabled() {
        return mPreferences.getBoolean(TOUCHPAD_NAVIGATION_ENABLED, true);
    }

    public boolean isMissingSensorsGeneratorEnabled() {
        return mPreferences.getBoolean(GENERATE_MISSING_SENSORS, false);
    }

    public boolean isRestrictedAudioFocusUsed() {
        return mPreferences.getBoolean(PREF_KEY_USE_RESTRICTED_AUDIO_FOCUS_MODES, false);
    }

    public boolean isFpsLimited() {
        return mPreferences.getBoolean(PREF_KEY_FPS_30, false);
    }

    public boolean isUseTabletCompassEnabled() {
        return mPreferences.getBoolean(USE_TABLET_COMPASS, false);
    }

    public boolean isObd2Enabled() {
        return mPreferences.getBoolean(OBD2_ENABLED, false);
    }

    public String getObd2Device() {
        return mPreferences.getString(OBD2_DEVICE, null);
    }

    public boolean isAAcEnabledForMedia() {
        return mPreferences.getBoolean(AUDIO_AAC_ENABLED_MEDIA, true); // enable by default
    }

    public boolean isAAcEnabledForTts() {
        return mPreferences.getBoolean(AUDIO_AAC_ENABLED_TTS, false); // disable by default
    }

    public boolean autoStartLocalMode() {
        return mPreferences.getBoolean(AUTO_START_LOCAL_MODE, false);
    }

    public boolean useSunCalculator()  {
        return mPreferences.getBoolean(USE_SUN_CALCULATOR, false);
    }

    public boolean grantFocusOnStart() {
        return mPreferences.getBoolean(AUTO_START_PROJECTION, true);
    }

    public boolean volumeToggleDebug() {
        return mPreferences.getBoolean(VOLUME_TOGGLE_DEBUG, false);
    }

    public boolean showVideoFocusDialog() {
        return mPreferences.getBoolean(SHOW_VIDEO_FOCUS_DIALOG, false);
    }
}

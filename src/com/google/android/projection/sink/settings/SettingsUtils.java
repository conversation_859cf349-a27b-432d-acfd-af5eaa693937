// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.settings;

import android.content.SharedPreferences;
import android.preference.ListPreference;
import android.preference.Preference;
import android.preference.PreferenceManager;

/**
 * Helper for handling settings dialogs
 */
public class SettingsUtils {
    private SettingsUtils() {
    }

    /**
     * Binds a preference's summary to its value. More specifically, when the
     * preference's value is changed, its summary (line of text below the
     * preference title) is updated to reflect the value. The summary is also
     * immediately updated upon calling this method. The exact display format
     * depends on the type of preference.
     */
    public static void bindPreferenceSummaryToValue(Preference preference,
            Preference.OnPreferenceChangeListener listener) {
        // Set the listener to watch for value changes.
        preference.setOnPreferenceChangeListener(listener);

        // Trigger the listener immediately with the preference's
        // current value.
        syncSummaryToValue(preference);
    }

    /**
     * Set preference's summary to preference's value.
     */
    public static void syncSummaryToValue(Preference preference) {
        SharedPreferences prefs = PreferenceManager
                .getDefaultSharedPreferences(preference.getContext());
        String value;
        try {
            value = prefs.getString(preference.getKey(), "");
        } catch (ClassCastException e) {
            value = "" + prefs.getInt(preference.getKey(), 0);
        }
        updatePreferenceSummary(preference, value);
    }

    /**
     * A preference value updates the preference's summary to reflect its new
     * value.
     */
    public static boolean updatePreferenceSummary(Preference preference, Object value) {
        if (value == null) {
            return false;
        }

        String stringValue = value.toString();

        if (preference instanceof ListPreference) {
            // For list preferences, look up the correct display value in
            // the preference's 'entries' list.
            ListPreference listPreference = (ListPreference) preference;
            int index = listPreference.findIndexOfValue(stringValue);

            // Set the summary to reflect the new value.
            preference.setSummary(index >= 0 ? listPreference.getEntries()[index] : null);
        } else {
            // For all other preferences, set the summary to the value's
            // simple string representation.
            preference.setSummary(stringValue);
        }
        return true;
    }

}

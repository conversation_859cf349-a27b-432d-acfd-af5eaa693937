// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink;

import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.util.Log;
import com.google.android.projection.proto.Protos.NavigationCurrentPosition;
import com.google.android.projection.proto.Protos.NavigationNextTurnEvent;
import com.google.android.projection.proto.Protos.NavigationState;
import com.google.android.projection.proto.Protos.NavigationStatus.NavigationStatusEnum;
import com.google.android.projection.proto.Protos.PhoneStatus.Call;
import com.google.android.projection.protocol.AndroidHeadUnitLogging;
import com.google.android.projection.protocol.MediaBrowser.MediaBrowserListener;
import com.google.android.projection.protocol.MediaBrowser.MediaList;
import com.google.android.projection.protocol.MediaBrowser.MediaSong;
import com.google.android.projection.protocol.MediaBrowser.MediaSource;
import com.google.android.projection.protocol.MediaPlaybackStatus.MediaPlaybackStatusListener;
import com.google.android.projection.protocol.NavigationStatus.NavigationStatusListener;
import com.google.android.projection.protocol.PhoneStatus.PhoneStatusListener;

/**
 * Callbacks called by protocol endpoints to update instrument panel information in debug panel.
 */
public class InstrumentClusterListener
        implements NavigationStatusListener, MediaPlaybackStatusListener, MediaBrowserListener,
                PhoneStatusListener {
  private static final String TAG = AndroidHeadUnitLogging.TAG_NAVIGATION;

    public static final int UPDATE_VTOGGLE_TEXT = 1;
    public static final int UPDATE_IC_NAVIGATION = 2;
    public static final int UPDATE_IC_MEDIA_STATUS = 3;
    public static final int UPDATE_IC_MEDIA_BROWSER = 4;
    public static final int UPDATE_IC_PHONE_STATUS = 5;
    public static final String NAVIGATION_BITMAP = "Navigation Bitmap";
    public static final String NAVIGATION_STRING = "Navigation String";
    public static final String MEDIA_STATUS_STRING = "Media Status String";
    public static final String MEDIA_ALBUM_ART = "Media Album Art";
    public static final String MEDIA_BROWSER_ROOT_STRING = "Media Browser Root String";
    public static final String MEDIA_BROWSER_SOURCE_STRING = "Media Browser Source String";
    public static final String MEDIA_BROWSER_LIST_STRING = "Media Browser List String";
    public static final String MEDIA_BROWSER_SONG_STRING = "Media Browser Song String";
    public static final String PHONE_STATUS_STRING = "Phone Status String";
    public static final String PHONE_STATUS_PHOTO = "Phone Status Photo";

    private NavigationStatusEnum mStatus;
    private byte[] mImage;
    private String mRoad;
    private NavigationNextTurnEvent.TurnSide mTurnSide;
    private NavigationNextTurnEvent.NextTurnEnum mEvent;
    private int mTurnAngle;
    private int mTurnNumber;
    private int mDistanceMeters;
    private int mTimeSeconds;
    private final Handler mNativePanelUpdateHandler;
    private String mSong;
    private String mAlbum;
    private String mArtist;
    private byte[] mAlbumArt;
    private String mPlaylist;
    private int mDurationSeconds;
    private int mRatings;
    private int mState;
    private String mSource;
    private int mPlaybackSeconds;
    private boolean mShuffle;
    private boolean mRepeat;
    private boolean mRepeatOne;

    public InstrumentClusterListener() {
        mNativePanelUpdateHandler = new Handler(Looper.getMainLooper());
    }

    public InstrumentClusterListener(Handler nativePanelUpdateHandler) {
        mNativePanelUpdateHandler = nativePanelUpdateHandler;
    }

    @Override
    public void onStatus(int status) {
        mStatus = NavigationStatusEnum.valueOf(status);
        updateNavigationViews();
    }

    @Override
    public void onNextTurn(String road, int turnSide, int event, byte[] image, int turnAngle,
            int turnNumber) {
        mRoad = road;
        mTurnSide = NavigationNextTurnEvent.TurnSide.valueOf(turnSide);
        mEvent = NavigationNextTurnEvent.NextTurnEnum.valueOf(event);
        mTurnAngle = turnAngle;
        mTurnNumber = turnNumber;
        mImage = image;
        updateNavigationViews();
    }

    // TODO: delete this function once nothing depends on it
    public void onNextTurnDistance(int distanceMeters, int timeSeconds,
            float displayDistance, int displayDistanceUnit) {
    }

    @Override
    public void onNextTurnDistance(int distanceMeters,
            int timeSeconds, int displayDistanceE3, int displayDistanceUnit) {
        onNextTurnDistance(
                distanceMeters, timeSeconds, displayDistanceE3 * 1e-3f, displayDistanceUnit);
        mDistanceMeters = distanceMeters;
        mTimeSeconds = timeSeconds;
        updateNavigationViews();
    }

  @Override
  public void onNavigationState(NavigationState navigationState) {
    Log.i(TAG, "Not supported operations.");
  }

  @Override
  public void onNavigationCurrentPosition(NavigationCurrentPosition navigationCurrentPosition) {
    Log.i(TAG, "Not supported operations.");
  }

    private void updateNavigationViews() {
        StringBuilder sb = new StringBuilder();
        if (mStatus != null) {
            sb.append("Status: ");
            if (mStatus == NavigationStatusEnum.ACTIVE) {
                sb.append("Active");
            } else if (mStatus == NavigationStatusEnum.UNAVAILABLE) {
                sb.append("Unavailable");
            } else if (mStatus == NavigationStatusEnum.INACTIVE) {
                sb.append("Inactive");
            }
            sb.append("\n");
        }
        if (mStatus == NavigationStatusEnum.ACTIVE) {
            if (mRoad != null) {
                sb.append("Road: " + mRoad + "\n");
            }
            if (mTurnSide != null) {
                sb.append("Turn: ");
                if (mTurnSide == NavigationNextTurnEvent.TurnSide.LEFT) {
                    sb.append("Left");
                } else if (mTurnSide == NavigationNextTurnEvent.TurnSide.RIGHT) {
                    sb.append("Right");
                } else if (mTurnSide == NavigationNextTurnEvent.TurnSide.UNSPECIFIED) {
                    sb.append("Unspecified");
                }
                sb.append("\n");
            }
            if (mEvent != null) {
                sb.append("Event: ");
                if (mEvent == NavigationNextTurnEvent.NextTurnEnum.DEPART) {
                    sb.append("Depart");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.DESTINATION) {
                    sb.append("Destination");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.FERRY_BOAT) {
                    sb.append("Ferry boat");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.FERRY_TRAIN) {
                    sb.append("Ferry train");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.FORK) {
                    sb.append("Fork");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.MERGE) {
                    sb.append("Merge");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.NAME_CHANGE) {
                    sb.append("Name change");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.OFF_RAMP) {
                    sb.append("Off ramp");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.ON_RAMP) {
                    sb.append("On ramp");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.ROUNDABOUT_ENTER) {
                    sb.append("Roundabout enter");
                } else if (mEvent ==
                        NavigationNextTurnEvent.NextTurnEnum.ROUNDABOUT_ENTER_AND_EXIT) {
                    sb.append("Roundabout enter and exit");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.ROUNDABOUT_EXIT) {
                    sb.append("Roundabout exit");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.SHARP_TURN) {
                    sb.append("Sharp turn");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.SLIGHT_TURN) {
                    sb.append("Slight turn");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.STRAIGHT) {
                    sb.append("Straight");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.TURN) {
                    sb.append("Turn");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.U_TURN) {
                    sb.append("U-Turn");
                } else if (mEvent == NavigationNextTurnEvent.NextTurnEnum.UNKNOWN) {
                    sb.append("Unknown");
                }
                sb.append("\n");
            }
            if (mEvent == NavigationNextTurnEvent.NextTurnEnum.ROUNDABOUT_ENTER_AND_EXIT) {
                sb.append("Turn Angle: " + mTurnAngle + "\n");
                sb.append("Turn Number: " + mTurnNumber + "\n");
            }
            sb.append("Distance: " + mDistanceMeters + "m\n");
            sb.append("Time: " + mTimeSeconds + "s\n");
        }

        Bundle data = new Bundle();
        data.putString(NAVIGATION_STRING, sb.toString());
        data.putByteArray(NAVIGATION_BITMAP, mImage);
        Message msg = new Message();
        msg.setData(data);
        msg.what = UPDATE_IC_NAVIGATION;

        mNativePanelUpdateHandler.sendMessage(msg);
    }

    @Override
    public void onMediaStatus(int state, String source, int playbackSeconds,
            boolean shuffle, boolean repeat, boolean repeatOne) {
        mState = state;
        mSource = source;
        mPlaybackSeconds = playbackSeconds;
        mShuffle = shuffle;
        mRepeat = repeat;
        mRepeatOne = repeatOne;
        updateMedia();
    }

    @Override
    public void onMediaMetadata(String song, String album,
            String artist, byte[] albumArt, String playlist, int durationSeconds, int ratings) {
        mSong = song;
        mAlbum = album;
        mArtist = artist;
        mAlbumArt = albumArt;
        mPlaylist = playlist;
        mDurationSeconds = durationSeconds;
        mRatings = ratings;
        updateMedia();
    }

    @Override
    public void onMediaRootNode(String path, MediaSource[] sources) {
        StringBuilder sb = new StringBuilder();
        sb.append("Root Node: " + path + "\n");
        for (MediaSource source : sources) {
            sb.append(source.name + " " + source.path + "\n");
        }
        Bundle data = new Bundle();
        data.putString(MEDIA_BROWSER_ROOT_STRING, sb.toString());
        Message msg = new Message();
        msg.setData(data);
        msg.what = UPDATE_IC_MEDIA_BROWSER;

        mNativePanelUpdateHandler.sendMessage(msg);
    }

    @Override
    public void onMediaSourceNode(MediaSource source, int start, int total, MediaList[] lists) {
        StringBuilder sb = new StringBuilder();
        sb.append("Source Node: " + source.path + " " +  source.name + "\n");
        Bundle data = new Bundle();
        data.putString(MEDIA_BROWSER_SOURCE_STRING, sb.toString());
        Message msg = new Message();
        msg.setData(data);
        msg.what = UPDATE_IC_MEDIA_BROWSER;

        mNativePanelUpdateHandler.sendMessage(msg);
    }

    @Override
    public void onMediaListNode(MediaList list, int start, int total, MediaSong[] songs) {
        StringBuilder sb = new StringBuilder();
        sb.append("List Node: " + list.path + " " + list.name + "\n");
        Bundle data = new Bundle();
        data.putString(MEDIA_BROWSER_LIST_STRING, sb.toString());
        Message msg = new Message();
        msg.setData(data);
        msg.what = UPDATE_IC_MEDIA_BROWSER;

        mNativePanelUpdateHandler.sendMessage(msg);
    }

    @Override
    public void onMediaSongNode(MediaSong song, byte[] album_art, int durationSeconds) {
        StringBuilder sb = new StringBuilder();
        sb.append("Song Node: " + song.path + "\n");
        sb.append(song.name + " - " + song.artist + " - " + song.album);
        Bundle data = new Bundle();
        data.putString(MEDIA_BROWSER_SONG_STRING, sb.toString());
        Message msg = new Message();
        msg.setData(data);
        msg.what = UPDATE_IC_MEDIA_BROWSER;

        mNativePanelUpdateHandler.sendMessage(msg);
    }

    @Override
    public void onPhoneStatus(Call[] calls, int signalStrength) {
        StringBuilder sb = new StringBuilder();
        for (Call call : calls) {
            if (call.hasCallerNumber()) {
                sb.append(call.getCallerNumber() + " ");
            }
            if (call.hasCallerId()) {
                sb.append(call.getCallerId() + " ");
            }
            if (call.hasCallerNumberType()) {
                sb.append(call.getCallerNumberType());
            }
        }
        Bundle data = new Bundle();
        data.putString(PHONE_STATUS_STRING, sb.toString());
        if (calls.length > 0 && calls[0].hasCallerThumbnail()) {
            data.putByteArray(PHONE_STATUS_PHOTO, calls[0].getCallerThumbnail().toByteArray());
        }
        Message msg = new Message();
        msg.setData(data);
        msg.what = UPDATE_IC_PHONE_STATUS;

        mNativePanelUpdateHandler.sendMessage(msg);
    }


    private void updateMedia() {
        Bundle data = new Bundle();
        StringBuilder sb = new StringBuilder();
        sb.append("State: " + mState + "\n");
        sb.append("Source: " + mSource + "\n");
        sb.append("Song: " + mSong + "\n");
        sb.append("Album: " + mAlbum + "\n");
        sb.append("Artist: " + mArtist + "\n");
        sb.append("Playlist: " + mPlaylist + "\n");
        sb.append("Duration: " + mDurationSeconds + "\n");
        sb.append("Playback: " + mPlaybackSeconds + "\n");
        sb.append("Ratings: " + mRatings + "\n");
        sb.append("Shuffle: " + mShuffle + "\n");
        sb.append("Repeat: " + mRepeat + "\n");
        sb.append("Repeat One: " + mRepeatOne + "\n");
        data.putString(MEDIA_STATUS_STRING, sb.toString());
        if (mAlbumArt != null) {
            data.putByteArray(MEDIA_ALBUM_ART, mAlbumArt);
        }
        Message msg = new Message();
        msg.setData(data);
        msg.what = UPDATE_IC_MEDIA_STATUS;

        mNativePanelUpdateHandler.sendMessage(msg);
    }
}

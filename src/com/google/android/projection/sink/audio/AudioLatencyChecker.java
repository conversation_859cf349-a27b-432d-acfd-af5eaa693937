// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.audio;

import android.view.MotionEvent;

/**
 * Tool to check latency. To use this, MainActivity.DBG_LATENCY should be true.
 * Latency detection is based on the idea that music will follow a user input event.
 * When MainActivity detects audio data, latency will be calculated based on the last user input.
 */
public class AudioLatencyChecker {
    private static final int MIN_AUDIO_LATENCY = 100;
    // For audio latency.
    private long mLastPresentationTimeUs;
    private long mInputEventTimeMs = 0;
    private long mLatestLatency;
    private boolean mLatencyCheckOngoing = false;

    public synchronized void onInputEvent(MotionEvent event) {
        // ACTION_DOWN is ignored as typically it does not trigger UI action.
        // But ACTION_DOWN resets latency check.
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            mLatencyCheckOngoing = false;
            return;
        }
        mInputEventTimeMs = System.currentTimeMillis();
        mLastPresentationTimeUs = 0;
        mLatencyCheckOngoing = true;
    }

    public void onReceived(long presentationTimeUs) {
        if (mLastPresentationTimeUs == 0) {
            mLastPresentationTimeUs = presentationTimeUs;
        }
    }

    public void onDecoded(long presentationTimeUs) {
        if (presentationTimeUs == mLastPresentationTimeUs && mLatencyCheckOngoing &&
                presentationTimeUs != 0) {
            mLatencyCheckOngoing = false;
            long latency = System.currentTimeMillis() - mInputEventTimeMs;
            if (latency > MIN_AUDIO_LATENCY) {
                mLatestLatency = latency;
            }
            mLastPresentationTimeUs = 0;
        }
    }

    public long getLatestLatency() {
        return mLatestLatency;
    }
}

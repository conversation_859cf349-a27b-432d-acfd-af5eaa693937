// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.audio;

import android.media.AudioManager;
import android.util.Log;

import com.google.android.projection.protocol.AndroidHeadUnitLogging;
import com.google.android.projection.protocol.GalReceiver;
import com.google.android.projection.protocol.GalVerificationVendorExtension;
import com.google.android.projection.sink.GalIntegration;

import java.util.LinkedList;

/**
 * An example of how to handle audio focus transitions and handling audio requests from the phone.
 */
public class AudioFocusManager implements GalVerificationVendorExtension.AudioFocusManager {
    private static final String TAG = AndroidHeadUnitLogging.TAG_AUDIO;
    private static final boolean DBG = true;

    private final GalIntegration mGal;
    private final LinkedList<FocusChangeListener> mListeners
        = new LinkedList<FocusChangeListener>();
    private volatile boolean mPhoneFocusEnabled = true;
    private boolean mCarPlayingHighPrioritySound = false;
    private volatile int mPhoneFocusState = GalReceiver.AUDIO_FOCUS_STATE_LOSS;
    private volatile boolean mCarMusicPlaying = false;
    private volatile boolean mCarGuidancePlaying = false;
    private volatile boolean mCarCanDuck = false;

    private final boolean mUseRestrictedMode;

    public interface FocusChangeListener {
        void onPhoneFocusChange(int state, boolean carCanDuck);
    }

    public AudioFocusManager(GalIntegration gal, boolean useRestrictedMode) {
        mGal = gal;
        mUseRestrictedMode = useRestrictedMode;
    }

    public boolean canPhonePlay(int streamType) {
        // system stream does not require focus.
        if (streamType == AudioManager.STREAM_SYSTEM) {
            return true;
        } else if (streamType == AudioManager.STREAM_MUSIC) {
            return ((mPhoneFocusState == GalReceiver.AUDIO_FOCUS_STATE_GAIN) ||
                    (mPhoneFocusState == GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT) ||
                    (mPhoneFocusState == GalReceiver.AUDIO_FOCUS_STATE_GAIN_MEDIA_ONLY) ||
                    (mPhoneFocusState == GalReceiver.AUDIO_FOCUS_STATE_LOSS_TRANSIENT_CAN_DUCK));
        }
        return ((mPhoneFocusState == GalReceiver.AUDIO_FOCUS_STATE_GAIN) ||
                (mPhoneFocusState == GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT) ||
                (mPhoneFocusState == GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT_GUIDANCE_ONLY));
    }

    public synchronized void registerFocusChangeListener(FocusChangeListener listener) {
        mListeners.add(listener);
    }

    public synchronized void unregisterFocusChangeListener(FocusChangeListener listener) {
        mListeners.remove(listener);
    }

    @Override
    public synchronized void enablePhoneFocus() {
        mPhoneFocusEnabled = true;
        setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_GAIN, true);
    }

    @Override
    public synchronized void disablePhoneFocus() {
        mPhoneFocusEnabled = false;
        setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_LOSS, true);
    }

    public synchronized void notifyCarMusicPlayStart() {
        if (DBG) {
            Log.i(TAG, "notifyCarMusicPlayStart");
        }
        mCarMusicPlaying = true;
        setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_LOSS, true);
    }

    public synchronized void notifyCarMusicPlayEnd() {
        if (DBG) {
            Log.i(TAG, "notifyCarMusicPlayEnd");
        }
        mCarMusicPlaying = false;
        if (mCarGuidancePlaying) {
            // car was playing music. So phone cannot be playing music. So there is only one
            // possible state.
            setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_LOSS_TRANSIENT, true);
        } else {
            // do not give it back as phone will request it when necessary.
        }
    }

    public boolean isCarPlayingMusic() {
        return mCarMusicPlaying;
    }

    public boolean isCarPlayingGuidance() {
        return mCarGuidancePlaying;
    }

    public boolean isCarPlayingHighPrioritySound() {
        return mCarPlayingHighPrioritySound;
    }

    public synchronized void notifyCarGuidancePlayStart(boolean phoneCanDuck) {
        if (DBG) {
            Log.i(TAG, "notifyCarGuidancePlayStart");
        }
        mCarGuidancePlaying = true;
        if (mPhoneFocusState == GalReceiver.AUDIO_FOCUS_STATE_LOSS) {
            return;
        }
        int phoneState = phoneCanDuck ?
                GalReceiver.AUDIO_FOCUS_STATE_LOSS_TRANSIENT_CAN_DUCK :
                    GalReceiver.AUDIO_FOCUS_STATE_LOSS_TRANSIENT;
        setPhoneFocusState(phoneState, true);
    }

    public synchronized void notifyCarGuidancePlayEnd() {
        if (DBG) {
            Log.i(TAG, "notifyCarGuidancePlayEnd");
        }
        mCarGuidancePlaying = false;
        if (mCarMusicPlaying) {
            // nothing to do. keep the current focus, which should be LOSS
            return;
        }
        // end of transient focus. So give it back to phone.
        setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_GAIN, true);
    }

    /**
     * check if car can continue its music if it is already started.
     */
    public synchronized boolean canCarContinueMusic() {
        if (mCarPlayingHighPrioritySound) {
            return false;
        }
        if (mCarCanDuck) {
            return true;
        }
        return (mPhoneFocusState != GalReceiver.AUDIO_FOCUS_STATE_GAIN) &&
                (mPhoneFocusState != GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT) &&
                (mPhoneFocusState != GalReceiver.AUDIO_FOCUS_STATE_LOSS_TRANSIENT_CAN_DUCK);
    }

    /**
     * check if car can continue its own guidance without having conflict with phone
     */
    public synchronized boolean canCarContinueGuidance() {
        if (mCarPlayingHighPrioritySound) {
            return false;
        }
        return (mPhoneFocusState != GalReceiver.AUDIO_FOCUS_STATE_GAIN) &&
                (mPhoneFocusState != GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT);
    }

    public synchronized void notifyCarHighPrioritySoundStart() {
        if (DBG) {
            Log.i(TAG, "notifyCarHighPrioritySoundStart");
        }
        mCarPlayingHighPrioritySound = true;
        if (mPhoneFocusState == GalReceiver.AUDIO_FOCUS_STATE_GAIN
                || mPhoneFocusState == GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT
                || mPhoneFocusState == GalReceiver.AUDIO_FOCUS_STATE_LOSS_TRANSIENT_CAN_DUCK) {
            setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_LOSS_TRANSIENT, true);
        } else { // just notify client so that client can stop
            notifyFocusStateToClients(mPhoneFocusState);
        }
    }

    public synchronized void notifyCarHighPrioritySoundEnd() {
        if (DBG) {
            Log.i(TAG, "notifyCarHighPrioritySoundEnd");
        }
        mCarPlayingHighPrioritySound = false;
        // do not resume anything automatically as this is safety critical alert.
        // expect user to request things explicitly later.
    }

    /*
     * Put phone into one of the transient loss states.
     * @param focusState one of {@link GalReceiver#AUDIO_FOCUS_STATE_LOSS_TRANSIENT},
     *        or {@link GalReceiver#AUDIO_FOCUS_STATE_LOSS_TRANSIENT}.
     */
    @Override
    public synchronized void setTransientLossState(int transientLossState) {
        if (transientLossState != GalReceiver.AUDIO_FOCUS_STATE_LOSS_TRANSIENT &&
                transientLossState != GalReceiver.AUDIO_FOCUS_STATE_LOSS_TRANSIENT_CAN_DUCK) {
            Log.e(TAG, String.format("Unknown transient loss focus state requested: %d",
                    transientLossState));
            return;
        }
        setPhoneFocusState(transientLossState, false);
        mPhoneFocusEnabled = false;
    }

    public synchronized void handleAudioFocusRequest(int request) {
        if (DBG) {
            Log.i(TAG, "onAudioFocusRequest, req:" + request + " highpriority:" +
                    mCarPlayingHighPrioritySound);
        }
        // This is only for testing this restricted mode. No special meaning for combination.
        if (mUseRestrictedMode && !mPhoneFocusEnabled &&
                (request == GalReceiver.AUDIO_FOCUS_GAIN)) {
            mCarMusicPlaying = false;
            setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_GAIN_MEDIA_ONLY, false);
        } else if (mCarPlayingHighPrioritySound || !mPhoneFocusEnabled) {
            setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_LOSS, false);
        } else {
            mCarCanDuck = false;
            if (request == GalReceiver.AUDIO_FOCUS_GAIN) {
                // car should stop its own music
                mCarMusicPlaying = false;
                setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_GAIN, false);
            } else if (request == GalReceiver.AUDIO_FOCUS_GAIN_TRANSIENT) {
                // car should stop its own music
                mCarMusicPlaying = false;
                if (!mUseRestrictedMode) {
                    setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT, false);
                } else {
                    setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT_GUIDANCE_ONLY,
                            false);
                }
            } else if (request == GalReceiver.AUDIO_FOCUS_GAIN_TRANSIENT_MAY_DUCK) {
                mCarCanDuck = true;
                if (!mUseRestrictedMode) {
                    setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT, false);
                } else {
                    setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT_GUIDANCE_ONLY,
                            false);
                }
            } else if (request == GalReceiver.AUDIO_FOCUS_RELEASE) {
                setPhoneFocusState(GalReceiver.AUDIO_FOCUS_STATE_LOSS, false);
            } else {
                Log.i(TAG, "Unknown focus request " + request);
            }
        }
    }

    private void notifyFocusStateToClients(int focusState) {
        if (DBG) {
            Log.i(TAG, "notifyFOcusStateToClients " + focusState);
        }
        for (FocusChangeListener listener: mListeners) {
            listener.onPhoneFocusChange(focusState, mCarCanDuck);
        }
    }

    private void setPhoneFocusState(int focusState, boolean unsolicited) {
        if (DBG) {
            Log.i(TAG, "set phone focus state:" + focusState);
        }
        // send it even if there is no state change to reply to request
        mGal.galReceiver.setAudioFocus(focusState, unsolicited);
        if (mPhoneFocusState != focusState) {
            notifyFocusStateToClients(focusState);
        }
        mPhoneFocusState = focusState;
    }
}

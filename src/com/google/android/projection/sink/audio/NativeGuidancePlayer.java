// Copyright 2014 Google Inc. All Rights Reserved.
package com.google.android.projection.sink.audio;

import android.content.Context;

import com.google.android.projection.protocol.GalReceiver;

/**
 * This is an example of handling audio focus when playing some navigation guidance messages.
 */
public class NativeGuidancePlayer extends NativeAudioPlayerBase {

    private boolean mStopping = false;

    public static NativeGuidancePlayer createGuidancePlayer(Context context,
            AudioFocusManager audioFocusManager,
            int resourceId) {
        NativeGuidancePlayer guidancePlayer = new NativeGuidancePlayer(audioFocusManager);
        guidancePlayer.init(context, resourceId, false);
        return guidancePlayer;
    }

    private NativeGuidancePlayer(AudioFocusManager audioFocusManager) {
        super(audioFocusManager);
    }

    @Override
    public void play() {
        if (!isPlaying()) {
            getAudioFocusManager().notifyCarGuidancePlayStart(true);
        }
        super.play();
    }

    @Override
    public void stop() {
        if (isPlaying()) {
            mStopping = true;
            getAudioFocusManager().notifyCarGuidancePlayEnd();
        }
        super.stop();
        mStopping = false;
    }

    @Override
    public void onPhoneFocusChange(int state, boolean carCanDuck) {
        // Here as an example, guidance is just stopped when phone request GAIN or GAIN_TRANSIENT.
        // Alternative is just to continue and finish playing.
        if ((state ==  GalReceiver.AUDIO_FOCUS_STATE_GAIN) ||
                (state == GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT)) {
            if (!mStopping) {
                stop();
            }
        }
    }
}

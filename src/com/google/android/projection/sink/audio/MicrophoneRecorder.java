// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.audio;

import android.media.AudioFormat;
import android.media.AudioRecord;
import android.media.MediaRecorder.AudioSource;
import android.util.Log;

import com.google.android.projection.protocol.AndroidHeadUnitLogging;

import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;

public class MicrophoneRecorder implements
        com.google.android.projection.protocol.AudioSource.AudioSourceListener {
    private static final String TAG = AndroidHeadUnitLogging.TAG_AUDIO_MIC;
    private static final int SAMPLING_RATE = 16000;
    private static final int NUMBER_OF_BITS = AudioFormat.ENCODING_PCM_16BIT;
    private static final int NUMBER_OF_CHANNELS = AudioFormat.CHANNEL_IN_MONO;
    private static final int CAPTURE_BUFFER_SIZE = 8192; // multiple of 4KB for AAC
    private static final int BUFFER_SIZE =  Math.max(AudioRecord.getMinBufferSize(SAMPLING_RATE,
            NUMBER_OF_CHANNELS, NUMBER_OF_BITS) * 4, CAPTURE_BUFFER_SIZE);

    private AudioSourceThread mAudioThread = new AudioSourceThread();
    private int mSessionId = 0;
    private Semaphore mUnackedFrames;
    private com.google.android.projection.protocol.AudioSource mGalAudioSource;

    public void setGalAudioSource(
            com.google.android.projection.protocol.AudioSource galAudioSource) {
        mGalAudioSource = galAudioSource;
    }

    public int getSamplingRate() {
        return SAMPLING_RATE;
    }

    @SuppressWarnings("unused")
    public int getNumberOfChannels() {
        if (NUMBER_OF_CHANNELS == AudioFormat.CHANNEL_IN_MONO) {
            return 1;
        }
        return 2;
    }

    @SuppressWarnings("unused")
    public int getNumberOfBits() {
        if (NUMBER_OF_BITS == AudioFormat.ENCODING_PCM_16BIT) {
            return 16;
        }
        return 8;
    }

    class AudioSourceThread extends Thread {
        private static final long ACK_TIMEOUT = 500;
        private static final long JOIN_TIMEOUT = 1000;
        private final Object mPauseLock = new Object();
        private volatile boolean mPaused = true;
        private volatile boolean mFinished = false;

        @Override
        public void run() {
            AudioRecord record = null;
            int framesSent = 0;
            while (!mFinished) {
                if (!mPaused && record == null) {
                    Log.i(TAG, "start recording");
                    record = new AudioRecord(AudioSource.MIC, SAMPLING_RATE,
                            NUMBER_OF_CHANNELS, NUMBER_OF_BITS, 4 * BUFFER_SIZE);
                    record.startRecording();
                    framesSent = 0;
                }
                while (!mPaused && record != null) {
                    byte[] buf = new byte[BUFFER_SIZE];
                    int toRead = BUFFER_SIZE;
                    while ((toRead > 0) && !isInterrupted()) {
                        int len = record.read(buf, BUFFER_SIZE - toRead, toRead);
                        if (len < 0) {
                            throw new RuntimeException("Error reading from AudioRecord " + len
                                    + " while trying to read " + (BUFFER_SIZE - toRead));
                        } else {
                            toRead -= len;
                        }
                    }
                    if (mFinished) {
                        break;
                    }
                    mGalAudioSource.send(0, buf);
                    framesSent++;
                    if (mFinished) {
                        break;
                    }
                    try {
                        if (!mUnackedFrames.tryAcquire(ACK_TIMEOUT, TimeUnit.MILLISECONDS)) {
                            Log.w(TAG, "mic ACK timeout, move on");
                        }
                    } catch (InterruptedException e) {
                        Log.e(TAG, "Dropping audio frame.");
                        break;
                    }
                }
                if (mPaused && record != null) {
                    Log.i(TAG, "stopping, sent frames " + framesSent);
                    record.stop();
                    record.release();
                    record = null;
                }
                synchronized(mPauseLock){
                    while (mPaused && !mFinished) {
                        try {
                            mPauseLock.wait();
                        } catch (InterruptedException e) {
                            break;
                        }
                    }
                }
            }
            Log.d(TAG, "Reached end of AudioThread");
        }

        public void onPause(){
            synchronized(mPauseLock) {
                mPaused = true;
            }
        }

        public void onResume() {
            synchronized(mPauseLock) {
                mPaused = false;
                mPauseLock.notifyAll();
            }
        }

        public void shutdown() {
            mFinished = true;
            mPaused = true;
            interrupt();
            // in case interrupt does not take out from wait().
            synchronized(mPauseLock) {
                mPauseLock.notifyAll();
            }
            try {
                join(JOIN_TIMEOUT);
            } catch (InterruptedException e) {
                //ignore
            }
        }
    }

    @Override
    public int microphoneRequestCallback(boolean open, boolean ancEnabled,
            boolean ecEnabled, int maxUnacked) {
        if (open) {
            mUnackedFrames = new Semaphore(maxUnacked);
            if (!mAudioThread.isAlive()) {
                mAudioThread.start();
            }
            mAudioThread.onResume();
        } else {
            mAudioThread.onPause();
        }
        return mSessionId++;
    }

    public void stop() {
        if (mAudioThread != null) {
            mAudioThread.shutdown();
            mAudioThread = null;
        }
    }

    @Override
    public void ackCallback(int sessionId, int ack) {
        mUnackedFrames.release(ack);
    }
}

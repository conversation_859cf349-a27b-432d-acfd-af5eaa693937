// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.audio;

import android.media.AudioFormat;
import android.media.AudioManager;
import android.media.AudioTrack;
import android.media.MediaCodec;
import android.media.MediaFormat;
import android.os.Handler;
import android.os.Looper;
import android.os.Message;
import android.os.Process;
import android.os.Trace;
import android.util.Log;
import android.util.Pair;

import com.google.android.projection.proto.Protos.MediaCodecType;
import com.google.android.projection.protocol.AndroidHeadUnitLogging;
import com.google.android.projection.protocol.AudioSink;

import java.nio.ByteBuffer;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.concurrent.ArrayBlockingQueue;
import java.util.concurrent.Semaphore;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * This is an example of playing audio coming from the phone and managing audio focus.
 */
public class AudioPlayer implements AudioSink.AudioListener, AudioFocusManager.FocusChangeListener,
        AudioTrack.OnPlaybackPositionUpdateListener {
    private static final String TAG = AndroidHeadUnitLogging.TAG_AUDIO;
    private static final boolean DBG = true;
    private static final boolean DBG_TRACE = true;

    private static final long POLLING_INTERVAL_MS = 200;
    private static final long DEQUEUE_INPUT_BUFFER_TIMEOUT_US = 300000;
    private static final long DEQUEUE_OUTPUT_BUFFER_TIMEOUT_US = 300000;
    private static final int DECODED_BUFFER_SIZE = 8192;

    private static final long MAX_STOP_WAIT_MS = 5000;

    private static final int SAMPLING_RATE_48KHZ = 48000;
    private static final int NUM_AUDIO_BUFFERS_TO_START_PLAY_DEFAULT = 2;
    private static final int NUM_AUDIO_BUFFERS_TO_START_PLAY_48KHZ = 3;

    private boolean mIsEncoded;
    private final int mStreamType;
    private final int mSamplingRate;
    private final int mChannelConfig;
    private final int mAudioFormat;
    private AudioSink mAudioSink;

    private final ArrayBlockingQueue<AudioBuffer> mQ = new ArrayBlockingQueue<AudioBuffer>(32);
    private AudioTrack mAudioTrack;

    private AudioPlayThread mAudioPlayThread;

    // for aac, codec config is passed as null payload in AudioBuffer and actual data is set here.
    private byte[] mCodecConfig;

    private AudioReceptionThread mReceptionThread;
    private final AudioLatencyChecker mLatencyChecker;
    private AudioFocusManager mAudioFocusManager;
    // Pair first: session id, second : frames to ACK in that session
    // access to this should be synchronized
    private final LinkedList<Pair<Integer, AtomicInteger>> mFramesToAckForSession =
            new LinkedList<Pair<Integer, AtomicInteger>>();

    private final AtomicInteger mFramesReceived = new AtomicInteger(0);

    private final Handler mMainThreadHandler;

    private int mNumAudioByffersToStartPlay;

    /**
     * Params as in {@link android.media.AudioTrack#AudioTrack(int, int, int, int, int, int)} except
     * missing last two arguments.
     * @param streamType
     * @param samplingRate
     * @param channelConfig
     * @param audioFormat
     * @param latencyChecker
     */
    public AudioPlayer(int streamType, int samplingRate, int channelConfig, int audioFormat,
            AudioLatencyChecker latencyChecker) {
        mStreamType = streamType;
        mSamplingRate = samplingRate;
        mChannelConfig = channelConfig;
        mAudioFormat = audioFormat;
        mLatencyChecker = latencyChecker;
        mMainThreadHandler = new Handler(Looper.getMainLooper());
    }

    public synchronized void registerAudioSink(AudioSink audioSink) {
        mAudioSink = audioSink;
    }

    public synchronized void registerAudioFocusManager(AudioFocusManager audioFocusManager) {
        mAudioFocusManager = audioFocusManager;
    }

    @Override
    public synchronized void onSetUp(int mediaCodecType) {
        Log.d(TAG, "audio onSetUp " + mStreamType);
        if (mediaCodecType == MediaCodecType.MEDIA_CODEC_AUDIO_PCM.getNumber()) {
            mIsEncoded = false;
            mAudioPlayThread = new PcmPlayThread();
        } else if (mediaCodecType == MediaCodecType.MEDIA_CODEC_AUDIO_AAC_LC.getNumber()) {
            mIsEncoded = true;
            mAudioPlayThread = new AacPlayThread();
        } else {
            throw new RuntimeException("Unsupported media codec type: " + mediaCodecType);
        }
        // At least two buffers to prevent one buffer delay to cause underrun
        // AAC 48kHz requires little bit more
        mNumAudioByffersToStartPlay = NUM_AUDIO_BUFFERS_TO_START_PLAY_DEFAULT;
        if (mIsEncoded && mSamplingRate == SAMPLING_RATE_48KHZ) {
            mNumAudioByffersToStartPlay = NUM_AUDIO_BUFFERS_TO_START_PLAY_48KHZ;
        }
        createAudioTrack();
        mAudioPlayThread.start();
        mReceptionThread = new AudioReceptionThread();
        mReceptionThread.init();
    }

    private void createAudioTrack() {
        int frameSize = getFrameSizeInSamples();
        int minBufferSize = Math.max(AudioTrack.getMinBufferSize(mSamplingRate, mChannelConfig,
                mAudioFormat), frameSize * 2 * ((mChannelConfig == AudioFormat.CHANNEL_OUT_STEREO) ?
                        2 : 1) * mNumAudioByffersToStartPlay);
        mAudioTrack = new AudioTrack(
                mStreamType, mSamplingRate, mChannelConfig,
                mAudioFormat, minBufferSize, AudioTrack.MODE_STREAM);
        Log.i(TAG, "AudioTrack minbuffer " + minBufferSize);
        mAudioTrack.setPlaybackPositionUpdateListener(AudioPlayer.this,
                mMainThreadHandler);
        mAudioTrack.setPositionNotificationPeriod(getFrameSizeInSamples());
    }

    @Override
    public synchronized void onStart(int sessionId) {
        Log.d(TAG, "audio onStart " + mStreamType);
        if (!mAudioFocusManager.canPhonePlay(mStreamType)) {
            Log.w(TAG, "audio start while no focus " + mStreamType);
            return;
        }
        mFramesReceived.set(0);
        mReceptionThread.startAudio();
    }

    @Override
    public synchronized void onStop(int sessionId) {
        Log.d(TAG, "audio onStop " + mStreamType);
        mReceptionThread.stopAudio();
    }

    @Override
    public synchronized void onShutdown() {
        if (mReceptionThread != null) {
            mReceptionThread.stopAudio();
            mReceptionThread.release();
        }
    }

    @Override
    public synchronized void onAudioData(int sessionId, long timestamp, final byte[] data) {
        if (Log.isLoggable(TAG, Log.VERBOSE)) {
            Log.v(TAG, "onAudioData, payload:" + data.length);
        }
        if (!mAudioFocusManager.canPhonePlay(mStreamType)) {
            Log.w(TAG, "audio data from phone without focus");
            // Possibly phone has released focus too early.
            // send ACK even if the data is thrown away
            mAudioSink.ackFrames(sessionId, 1);
            return;
        }
        mFramesReceived.incrementAndGet();
        AudioBuffer audioBuffer = new AudioBuffer();
        audioBuffer.presentationTimeUs = timestamp;
        audioBuffer.payload = data;
        mReceptionThread.receiveAudio(sessionId, audioBuffer);
    }

    @Override
    public void onCodecConfig(byte[] data) {
        Log.i(TAG, "onCodecConfig " + data.length + " " + data[0] + " " + data[1]);
        AudioBuffer audioBuffer = new AudioBuffer();
        mCodecConfig = data;
        audioBuffer.presentationTimeUs = 0;
        audioBuffer.payload = null;
        mReceptionThread.receiveAudio(0, audioBuffer);
    }

    /** stop and tear-down */
    public synchronized void tearDown() {
        safeStop(true);
        onShutdown();
        mQ.clear();
        if (mAudioPlayThread != null) {
            mAudioPlayThread.release();
        }
        if (mAudioTrack != null) {
            mAudioTrack.release();
            mAudioTrack = null;
        }
    }

    @Override
    public synchronized void onPhoneFocusChange(int state, boolean carCanDuck) {
        Log.i(TAG, "onPhoneFocusChange, state:" + state);
        if (!mAudioFocusManager.canPhonePlay(mStreamType) && mReceptionThread != null) {
            mReceptionThread.stopAudio();
        }
    }

    private synchronized void safePlay() {
        if (mAudioPlayThread != null && !mAudioPlayThread.isPlaying()) {
            mAudioPlayThread.startPlaying();
        }
    }

    /**
     * stop audio playing.
     * @param flush if true, flush all queued data. Otherwise, finish playing and stop.
     */
    private synchronized void safeStop(boolean flush) {
        if (mAudioPlayThread != null && mAudioPlayThread.isPlaying()) {
            if (DBG) {
                Log.i(TAG, "stop for stream " + mStreamType);
            }
            mAudioPlayThread.stopPlaying();
            if (flush) {
                mQ.clear();
            }
        }
    }

    @Override
    public void onMarkerReached(AudioTrack track) {
        // ignore
    }

    @Override
    public void onPeriodicNotification(AudioTrack track) {
        ackAFrame();
    }

    private void ackAFrame() {
        Pair<Integer, AtomicInteger> pair = getSessionPairForFirstSession();
        if (pair == null) {
            return;
        }
        AtomicInteger framesToAck = pair.second;
        int ackOld = framesToAck.getAndDecrement();
        if (ackOld > 0) {
            mAudioSink.ackFrames(pair.first, 1);
        } else {
            Log.w(TAG, "try to ack frame while ack <= 0");
        }
    }

    private void ackAllFrames(int sessionId, AtomicInteger framesToAck) {
        int ackOld = framesToAck.get();
        while (!framesToAck.compareAndSet(ackOld, 0)) {
            ackOld = framesToAck.get();
        }
        if (ackOld > 0) {
            mAudioSink.ackFrames(sessionId, ackOld);
        }
    }

    private int getFrameSizeInBytes() {
        int multiplier;
        if (mStreamType == AudioManager.STREAM_MUSIC) {
            multiplier = 4;
        } else {
            multiplier = 2;
        }
        return multiplier * getFrameSizeInSamples();
    }

    private int getFrameSizeInSamples() {
        if (mIsEncoded) {
            return 1024;
        }
        if (mStreamType == AudioManager.STREAM_MUSIC) {
            return 2048;
        } else {
            return 1024;
        }
    }

    private AtomicInteger getFramesToAck(int sessionId) {
        AtomicInteger framesToAck = null;
        synchronized (mFramesToAckForSession) {
            for (Pair<Integer, AtomicInteger> pair: mFramesToAckForSession) {
                if (pair.first.intValue() == sessionId) {
                    framesToAck = pair.second;
                    break;
                }
            }
            if (framesToAck == null) {
                framesToAck = new AtomicInteger(0);
                mFramesToAckForSession.addLast(new Pair<Integer, AtomicInteger>(
                        new Integer(sessionId), framesToAck));
            }
        }
        return framesToAck;
    }

    private Pair<Integer, AtomicInteger> getSessionPairForFirstSession() {
        synchronized (mFramesToAckForSession) {
            return mFramesToAckForSession.peek();
        }
    }

    private Pair<Integer, AtomicInteger> removeFirstSession() {
        synchronized (mFramesToAckForSession) {
            return mFramesToAckForSession.pollFirst();
        }
    }

    private class AudioBuffer {
        public long presentationTimeUs = 0;
        public byte[] payload;
    }

    private abstract class AudioPlayThread extends Thread {
        private boolean mIsPlaying = false;
        private boolean mShouldQuit = false;
        protected final Semaphore mStopWait = new Semaphore(0);

        public AudioPlayThread(String threadName) {
            super(threadName);
        }

        public synchronized void startPlaying() {
            Log.i(TAG, "startPlaying " + mStreamType);
            mIsPlaying = true;
            notify();
        }

        public void stopPlaying() {
            Log.i(TAG, "stopPlaying " + mStreamType);
            synchronized (this) {
                if (!mIsPlaying) {
                    return;
                }
                mStopWait.drainPermits();
                mIsPlaying = false;
                notify();
            }
            try {
                mStopWait.tryAcquire(MAX_STOP_WAIT_MS, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
            }
        }

        public void release() {
            synchronized (this) {
                mIsPlaying = false;
                mShouldQuit = true;
                notify();
            }
            try {
                join();
            } catch (InterruptedException e) {
            }
        }

        protected synchronized boolean isPlaying() {
            return mIsPlaying;
        }

        protected synchronized boolean shouldQuit() {
            return mShouldQuit;
        }

        protected synchronized void waitForEvent() {
            try {
                wait();
            } catch (InterruptedException e) {
            }
        }
    }

    private class PcmPlayThread extends AudioPlayThread {
        public PcmPlayThread() {
            super("Pcm-" + mStreamType);
        }

        @Override
        public void run() {
            Process.setThreadPriority(Process.THREAD_PRIORITY_AUDIO);
            Log.i(TAG, "PCM thread starts " + mStreamType + " " + this);
            while (!shouldQuit()) {
                waitForEvent();
                boolean started = false;
                int buffersWritten = 0;
                while (isPlaying() || mQ.peek() != null) {
                    AudioBuffer audioBuffer;
                    try {
                        audioBuffer = mQ.poll(POLLING_INTERVAL_MS, TimeUnit.MICROSECONDS);
                    } catch (InterruptedException e) {
                        continue;
                    }
                    if (audioBuffer != null) {
                        mAudioTrack.write(audioBuffer.payload, 0, audioBuffer.payload.length);
                        buffersWritten++;
                        if (!started && buffersWritten == mNumAudioByffersToStartPlay) {
                            Log.i(TAG, "start play");
                            mAudioTrack.play();
                            started = true;
                        }
                    }
                }
                if (!started) { // only for very short clip. So do noe need notification
                    mAudioTrack.play();
                }
                mAudioTrack.stop();
                mStopWait.release();
                if (mQ.size() != 0) {
                    Log.w(TAG, "data still left in Q, clearing " + mQ.size());
                    mQ.clear();
                }
                Log.i(TAG, "buffers written:" + buffersWritten);
            }
            Log.i(TAG, "PCM thread ends " + mStreamType + " " + this);
        }
    }

    private class AacPlayThread extends AudioPlayThread {
        private AacOutputHandlingThread mOutputHandingThread;
        private MediaCodec mAacDecoder;

        public AacPlayThread() {
            super("Aac-" + mStreamType);
        }

        @Override
        public void run() {
            Process.setThreadPriority(Process.THREAD_PRIORITY_AUDIO + 1);
            Log.i(TAG, "AAC thread starts " + mStreamType + " " + this);
            MediaFormat audioFormat = MediaFormat.createAudioFormat("audio/mp4a-latm",
                    mSamplingRate, (mChannelConfig == AudioFormat.CHANNEL_OUT_STEREO) ? 2 : 1);
            try {
                mAacDecoder = MediaCodec.createDecoderByType("audio/mp4a-latm");
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
            mAacDecoder.configure(audioFormat, null, null, 0);
            mAacDecoder.start();
            mOutputHandingThread = new AacOutputHandlingThread(mAacDecoder);
            mOutputHandingThread.start();
            boolean codecConfigSet = false;
            while (!shouldQuit()) {
                waitForEvent();
                if (isPlaying()) {
                    mOutputHandingThread.startPlaying();
                }
                ByteBuffer[] decoderInputBuffers = mAacDecoder.getInputBuffers();
                while (isPlaying() || mQ.peek() != null) {
                    AudioBuffer audioBuffer;
                    try {
                        audioBuffer = mQ.poll(POLLING_INTERVAL_MS, TimeUnit.MICROSECONDS);
                    } catch (InterruptedException e) {
                        continue;
                    }
                    if (audioBuffer == null) {
                        continue;
                    }
                    if (mLatencyChecker != null) {
                        mLatencyChecker.onDecoded(audioBuffer.presentationTimeUs);
                    }
                    byte[] payload = audioBuffer.payload;
                    boolean isCodecConfig = false;
                    if (payload == null) {
                        if (!codecConfigSet && mCodecConfig != null) {
                            payload = mCodecConfig;
                            codecConfigSet = true;
                            isCodecConfig = true;
                        } else {
                            Log.i(TAG, "payload null, codecconfigset:" + codecConfigSet  +
                                    " codecConfig:" + Arrays.toString(mCodecConfig));
                            continue;
                        }
                    }
                    if (Log.isLoggable(TAG, Log.VERBOSE)) {
                        Log.v(TAG, "payload " + Arrays.toString(payload)
                                + " isCodecConfig:" + isCodecConfig);
                    }

                    int inputBufferIndex = mAacDecoder.dequeueInputBuffer
                            (DEQUEUE_INPUT_BUFFER_TIMEOUT_US);
                    if (inputBufferIndex < 0) {
                        Log.w(TAG, "No input buffer available " + mStreamType);
                        continue;
                    }
                    decoderInputBuffers[inputBufferIndex].clear();
                    decoderInputBuffers[inputBufferIndex].put(payload);
                    mAacDecoder.queueInputBuffer(inputBufferIndex, 0, payload.length,
                            audioBuffer.presentationTimeUs,
                            isCodecConfig ? MediaCodec.BUFFER_FLAG_CODEC_CONFIG : 0);
                }
                mOutputHandingThread.stopPlaying();
                try {
                    mAacDecoder.flush();
                } catch (IllegalStateException e){
                }
                mStopWait.release();
                if (mQ.size() != 0) {
                    Log.w(TAG, "data still left in Q, clearing " + mQ.size());
                    mQ.clear();
                }
            }
            mOutputHandingThread.release();
            mAacDecoder.stop();
            mAacDecoder.release();
            Log.i(TAG, "AAC thread ends " + mStreamType + " " + this);
        }
    }

    private class AacOutputHandlingThread extends AudioPlayThread {
        private final MediaCodec mAacDecoder;
        private final byte[] mDecodedBuffer = new byte[DECODED_BUFFER_SIZE];

        public AacOutputHandlingThread(MediaCodec aacDecoder) {
            super("AacOutput-" + mStreamType);
            mAacDecoder = aacDecoder;
        }

        @Override
        public void run() {
            Process.setThreadPriority(Process.THREAD_PRIORITY_AUDIO);
            Log.i(TAG, "Aac output handling thread starts " + mStreamType + " " + this);
            while (!shouldQuit()) {
                waitForEvent();
                int bytesWritten = 0;
                int outputBufferIndex = 0;
                boolean started = false;
                ByteBuffer[] decoderOutputBuffers = mAacDecoder.getOutputBuffers();
                MediaCodec.BufferInfo bufferInfo = new MediaCodec.BufferInfo();
                int buffersWritten = 0;
                while (isPlaying() || outputBufferIndex >= 0) {
                    outputBufferIndex = mAacDecoder.dequeueOutputBuffer(
                            bufferInfo, DEQUEUE_OUTPUT_BUFFER_TIMEOUT_US);
                    if (outputBufferIndex == MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED) {
                        decoderOutputBuffers = mAacDecoder.getOutputBuffers();
                        continue;
                    } else if (outputBufferIndex < 0) {
                        // This can happen often when there comes data exceeding number of buffers.
                        // So this should not need any special attention.
                        continue;
                    }
                    ByteBuffer decodedData = decoderOutputBuffers[outputBufferIndex];
                    decodedData.position(bufferInfo.offset);
                    decodedData.limit(bufferInfo.offset + bufferInfo.size);
                    if ((bufferInfo.flags & MediaCodec.BUFFER_FLAG_CODEC_CONFIG) != 0) {
                        // ignore
                        mAacDecoder.releaseOutputBuffer(outputBufferIndex, false);
                    } else if ((bufferInfo.flags & MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED) != 0) {
                        decoderOutputBuffers =  mAacDecoder.getOutputBuffers();
                    } else if ((bufferInfo.flags & MediaCodec.BUFFER_FLAG_END_OF_STREAM) != 0) {
                        Log.w(TAG, "EOS from AAC decoder");
                    } else {
                        if (getFrameSizeInBytes() != bufferInfo.size) {
                            Log.w(TAG, " protocol buffer size " + getFrameSizeInBytes() +
                                    " while actual size " + bufferInfo.size);
                        }
                        decodedData.get(mDecodedBuffer, 0, bufferInfo.size);
                        mAacDecoder.releaseOutputBuffer(outputBufferIndex, false);
                        mAudioTrack.write(mDecodedBuffer, 0, bufferInfo.size);
                        buffersWritten++;
                        bytesWritten += bufferInfo.size;
                        if (!started && buffersWritten == mNumAudioByffersToStartPlay) {
                            Log.i(TAG, "start play");
                            mAudioTrack.play();
                            started = true;
                        }
                    }
                }
                if (!started) { // only for very short clip. So do not need notification
                    mAudioTrack.play();
                }
                mAudioTrack.stop();
                mStopWait.release();
                Log.i(TAG, "bytes written:" + bytesWritten);
            }
            Log.i(TAG, "Aac output thread ends " + mStreamType + " " + this);
        }
    }

    private class AudioReceptionThread {
        private static final long INIT_WAIT_TIME_MS = 5000;
        private final Semaphore mInitSemaphore = new Semaphore(0);
        private Looper mLooper;
        private ReceptionHandler mHandler;
        private int mPacketsReceivedSinceStart = 0;
        private volatile boolean mReleased = false;

        private final Thread mThread = new Thread() {
            @Override
            public void run() {
                Looper.prepare();
                mLooper = Looper.myLooper();
                mHandler = new ReceptionHandler();
                mInitSemaphore.release();
                Looper.loop();
            }
        };

        public void init() {
            mThread.start();
            try {
                mInitSemaphore.tryAcquire(INIT_WAIT_TIME_MS, TimeUnit.MILLISECONDS);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }

        public void release() {
            mReleased = true;
            mLooper.quit();
        }

        public void receiveAudio(int sessionId, AudioBuffer buffer) {
            if (mReleased) {
                return;
            }
            if (mLatencyChecker != null) {
                mLatencyChecker.onReceived(buffer.presentationTimeUs);
            }
            mHandler.sendMessage(mHandler.obtainMessage(ReceptionHandler.DO_RECEIVE, sessionId, 0,
                    buffer));
        }

        public void startAudio() {
            if (mReleased) {
                return;
            }
            mHandler.sendMessage(mHandler.obtainMessage(ReceptionHandler.DO_START));
        }

        public void stopAudio() {
            if (mReleased) {
                return;
            }
            mHandler.sendMessage(mHandler.obtainMessage(ReceptionHandler.DO_STOP));
        }

        /**
         * This can be improved by adjusting timeout based on audio config, but for simplicity,
         * use big enough value now. The value is set to somewhat big as latency can be bigger
         * during initial start-up.
         */
        private static final long BUFFER_ADD_TIMEOUT_MS = 2000;

        private void doReceive(int sessionId, AudioBuffer buffer) {
            // still a hack to handle codecconfig through normal data
            // codecconfig should not be ACKed
            if (buffer.payload != null) {
                AtomicInteger frameToAck = getFramesToAck(sessionId);
                frameToAck.incrementAndGet();
                mPacketsReceivedSinceStart++;
            }
            try {
                if (!mQ.offer(buffer, BUFFER_ADD_TIMEOUT_MS, TimeUnit.MILLISECONDS)) {
                    Log.e(TAG, "Dropping audio data " + mStreamType);
                }
            } catch (InterruptedException e) {
                //ignore
            }
        }

        private class ReceptionHandler extends Handler {
            private static final int DO_RECEIVE = 1;
            private static final int DO_START = 2;
            private static final int DO_STOP = 3;
            private boolean mStarted = false;

            @Override
            public void handleMessage(Message msg) {
                switch (msg.what) {
                    case DO_RECEIVE: {
                        if (DBG_TRACE) {
                            Trace.beginSection("audioData");
                        }
                        doReceive(msg.arg1, (AudioBuffer) msg.obj);
                        if (DBG_TRACE) {
                            Trace.endSection();
                        }
                        break;
                    }
                    case DO_START: {
                        mStarted = true;
                        if (DBG_TRACE) {
                            Trace.beginSection("audioStart");
                        }
                        mPacketsReceivedSinceStart = 0;
                        AudioPlayer.this.safePlay();
                        if (DBG_TRACE) {
                            Trace.endSection();
                        }
                        break;
                    }
                    case DO_STOP: {
                        if (!mStarted) {
                            return;
                        }
                        mStarted = false;
                        if (DBG_TRACE) {
                            Trace.beginSection("audioStop");
                        }
                        AudioPlayer.this.safeStop(false);
                        Log.i(TAG, "stream stopped, received:" + mPacketsReceivedSinceStart
                                + " type:" + mStreamType);
                        int totalReceived = mFramesReceived.get();
                        if (totalReceived != mPacketsReceivedSinceStart) {
                            Log.w(TAG, "Total received " + totalReceived + " while played only "
                                    + mPacketsReceivedSinceStart);
                        }
                        Pair<Integer, AtomicInteger> pair = removeFirstSession();
                        if (pair != null) {
                            int sessionId = pair.first.intValue();
                            AtomicInteger framesToAck = pair.second;
                            ackAllFrames(sessionId, framesToAck);
                        } else {
                            Log.e(TAG, "no first session");
                        }
                        if (DBG_TRACE) {
                            Trace.endSection();
                        }
                        break;
                    }
                }
            }
        }
    }
}

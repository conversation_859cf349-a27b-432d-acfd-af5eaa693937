// Copyright 2014 Google Inc. All Rights Reserved.
package com.google.android.projection.sink.audio;

import android.content.Context;

import com.google.android.projection.protocol.GalReceiver;

/**
 * This is an example of handling audio focus when playing music.
 */
public class NativeMusicPlayer extends NativeAudioPlayerBase {

    private boolean mStopping = false;

    public static NativeMusicPlayer createMusicPlayer(Context context,
            AudioFocusManager audioFocusManager,
            int resourceId) {
        NativeMusicPlayer musicPlayer = new NativeMusicPlayer(audioFocusManager);
        musicPlayer.init(context, resourceId, true);
        return musicPlayer;
    }

    private NativeMusicPlayer(AudioFocusManager audioFocusManager) {
        super(audioFocusManager);
    }

    @Override
    public void play() {
        if (!isPlaying()) {
            getAudioFocusManager().notifyCarMusicPlayStart();
        }
        super.play();
    }

    @Override
    public void stop() {
        if (isPlaying()) {
            mStopping = true;
            getAudioFocusManager().notifyCarMusicPlayEnd();
        }
        super.stop();
        mStopping = false;
    }

    @Override
    public void onPhoneFocusChange(int state, boolean carCanDuck) {
        if ((state ==  GalReceiver.AUDIO_FOCUS_STATE_GAIN) ||
                (state ==  GalReceiver.AUDIO_FOCUS_STATE_GAIN_MEDIA_ONLY)) {
            if (!mStopping) {
                stop();
            }
        } else if (state == GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT) {
            if (isPlaying()) {
                if (carCanDuck) {
                    doDuck();
                } else {
                    pause();
                }
            }
        } else if (state == GalReceiver.AUDIO_FOCUS_STATE_LOSS) {
            if (isPlaying()) {
                undoDuck();
                resume();
            }
        } else if (state == GalReceiver.AUDIO_FOCUS_STATE_GAIN_TRANSIENT_GUIDANCE_ONLY) {
            if (isPlaying()) {
                doDuck();
            }
        }
    }
}

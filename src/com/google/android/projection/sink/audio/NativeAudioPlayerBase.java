// Copyright 2014 Google Inc. All Rights Reserved.
package com.google.android.projection.sink.audio;

import android.content.Context;
import android.content.res.AssetFileDescriptor;
import android.media.AudioManager;
import android.media.MediaPlayer;
import android.util.Log;
import com.google.android.projection.protocol.AndroidHeadUnitLogging;
import java.io.IOException;

public abstract class NativeAudioPlayerBase implements AudioFocusManager.FocusChangeListener {
    private static final String TAG = AndroidHeadUnitLogging.TAG_NATIVE_AUDIO;

    private final AudioFocusManager mAudioFocusManager;
    private final MediaPlayer mPlayer = new MediaPlayer();

    private volatile boolean mPlaying = false;

    protected NativeAudioPlayerBase(AudioFocusManager audioFocusManager) {
        mAudioFocusManager = audioFocusManager;
        mPlayer.setOnErrorListener(mErrorListener);
        mPlayer.setAudioStreamType(AudioManager.STREAM_MUSIC);
    }

    /**
     * @param context
     * @param resourceId
     * @param repeat will repeat the source if true.
     */
    protected void init(Context context, int resourceId, boolean repeat) {
        mPlayer.setLooping(repeat);
        mPlayer.setVolume(1.0f, 1.0f);
        if (!repeat) {
            mPlayer.setOnCompletionListener(mCompletionListener);
        }

        try {
            AssetFileDescriptor afd =
                    context.getResources().openRawResourceFd(resourceId);
            if (afd == null) {
                throw new RuntimeException("no res");
            }
            mPlayer.setDataSource(afd.getFileDescriptor(), afd.getStartOffset(),
                    afd.getLength());
            afd.close();
            mPlayer.prepare();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

        mAudioFocusManager.registerFocusChangeListener(this);
    }

    public void play() {
        if (mPlaying) {
            Log.i(TAG, "native audio already playing");
            return;
        }
        Log.i(TAG, "native audio start playing");
        mPlaying = true;
        mPlayer.start();
    }

    public void stop() {
        if (!mPlaying) {
            Log.i(TAG, "native audio already stopped");
            return;
        }
        Log.i(TAG, "native audio stopping");
        mPlaying = false;
        mPlayer.stop();
        try {
            mPlayer.prepare();
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    public void release() {
        mPlayer.release();
    }

    protected void resume() {
        Log.i(TAG, "native audio resume");
        mPlayer.start();
    }

    protected void pause() {
        Log.i(TAG, "native audio pause");
        mPlayer.pause();
    }

    protected void doDuck() {
        mPlayer.setVolume(0.5f, 0.5f);
    }

    protected void undoDuck() {
        mPlayer.setVolume(1.0f, 1.0f);
    }

    protected boolean isPlaying() {
        return mPlaying;
    }

    protected AudioFocusManager getAudioFocusManager() {
        return mAudioFocusManager;
    }

    private final MediaPlayer.OnErrorListener mErrorListener = new MediaPlayer.OnErrorListener() {
        @Override
        public boolean onError(MediaPlayer mp, int what, int extra) {
            Log.e(TAG, "audio error what " + what + " extra " + extra);
            return false;
        }
    };
    private final MediaPlayer.OnCompletionListener mCompletionListener =
            new MediaPlayer.OnCompletionListener() {
        @Override
        public void onCompletion(MediaPlayer mp) {
            Log.i(TAG, "native audio completed");
            stop();
        }
    };
}

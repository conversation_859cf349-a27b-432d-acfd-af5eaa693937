package com.google.android.projection.sink.navigation;

import com.google.android.projection.protocol.GalReceiver;
import com.google.android.projection.sink.GalIntegration;
import java.util.List;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * An example of how to manage navigation focus transitions between native nav and phone nav.
 */
public class NavigationFocusManager {

    public interface NavigationFocusListener {
        void onNavigationFocusChanged(boolean useNativeNavigation);
    }

  private final List<NavigationFocusListener> mListeners = new CopyOnWriteArrayList<>();
    private final GalIntegration mGal;
    private boolean mUseNativeNavigation = true;

    public NavigationFocusManager(GalIntegration gal) {
        mGal = gal;
    }

    public void handleNavigationFocusRequest(int focusType) {
        // Always grant requested focus in this example.
        mGal.galReceiver.sendNavigationFocusState(focusType);

        boolean useNativeNavigation = focusType != GalReceiver.NAVIGATION_FOCUS_STATE_PROJECTED;
        dispatchNavigationFocus(useNativeNavigation);
    }

    public void enforceNavigationFocus(boolean useNativeNavigation) {
        mGal.galReceiver.sendNavigationFocusState(useNativeNavigation
                ? GalReceiver.NAVIGATION_FOCUS_STATE_NATIVE
                : GalReceiver.NAVIGATION_FOCUS_STATE_PROJECTED);
        dispatchNavigationFocus(useNativeNavigation);
    }

    public synchronized void registerListener(NavigationFocusListener listener) {
        if (mListeners.add(listener)) {
            // Send the last value to the new listener.
            listener.onNavigationFocusChanged(mUseNativeNavigation);
        }
    }

    public synchronized void unregisterListener(NavigationFocusListener listener) {
        mListeners.remove(listener);
    }

    private synchronized void dispatchNavigationFocus(boolean useNativeNavigation) {
        if (mUseNativeNavigation != useNativeNavigation) {
            mUseNativeNavigation = useNativeNavigation;
            for (NavigationFocusListener listener : mListeners) {
                listener.onNavigationFocusChanged(mUseNativeNavigation);
            }
        }
    }
}

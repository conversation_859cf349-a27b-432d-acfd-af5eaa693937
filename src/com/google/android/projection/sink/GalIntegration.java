package com.google.android.projection.sink;

import android.os.Handler;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.SurfaceView;
import android.view.View;
import com.google.android.projection.proto.Protos;
import com.google.android.projection.proto.Protos.DisplayType;
import com.google.android.projection.proto.Protos.EvConnectorType;
import com.google.android.projection.proto.Protos.FuelType;
import com.google.android.projection.proto.Protos.KeyCode;
import com.google.android.projection.proto.Protos.MediaCodecType;
import com.google.android.projection.proto.Protos.NavigationStatusService;
import com.google.android.projection.protocol.AudioSink;
import com.google.android.projection.protocol.AudioSource;
import com.google.android.projection.protocol.BluetoothEndpoint;
import com.google.android.projection.protocol.CarWifi;
import com.google.android.projection.protocol.EchoVendorExtension;
import com.google.android.projection.protocol.GalReceiver;
import com.google.android.projection.protocol.GalVerificationVendorExtension;
import com.google.android.projection.protocol.InputSource;
import com.google.android.projection.protocol.InputSource.InputInjector;
import com.google.android.projection.protocol.MediaBrowser;
import com.google.android.projection.protocol.MediaPlaybackStatus;
import com.google.android.projection.protocol.NavigationStatus;
import com.google.android.projection.protocol.PhoneStatus;
import com.google.android.projection.protocol.Radio;
import com.google.android.projection.protocol.RadioEndpoint;
import com.google.android.projection.protocol.SensorSource;
import com.google.android.projection.protocol.Transport;
import com.google.android.projection.protocol.VideoSink;
import com.google.android.projection.protocol.WifiProjectionEndpoint;
import com.google.android.projection.sink.audio.AudioFocusManager;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Contains GAL initialization and car-specific and application-specific assumptions.
 */
public class GalIntegration {
    public static final int AUDIO_SINK_INDEX_MEDIA = 0;
    public static final int AUDIO_SINK_INDEX_TTS = 1;
    public static final int AUDIO_SINK_INDEX_SYSTEM = 2;
    public static final int NUM_AUDIO_SINKS = 3;

  private static final int RADIO_SERVICE_ID = 1;
  private static final int WIFI_PROJECTION_SERVICE_ID = 2;
    private static final int SENSOR_SERVICE_ID = 3;
    private static final int AUDIO_SERVICE_MEDIA_ID = 4;
    private static final int AUDIO_SERVICE_TTS_ID = 5;
    private static final int AUDIO_SERVICE_SYSTEM_ID = 6;
    private static final int AUDIO_SOURCE_SERVICE_ID = 7;
    private static final int BLUETOOTH_SERVICE_ID = 8;
    private static final int NAVIGATION_STATUS_SERVICE_ID = 9;
    private static final int MEDIA_PLAYBACK_STATUS_SERVICE_ID = 10;
    private static final int MEDIA_BROWSE_SERVICE_ID = 11;
    private static final int PHONE_STATUS_SERVICE_ID = 12;
    private static final int GAL_VERIFICATION_VENDOR_EXTENSION_SERVICE_ID = 13;
    private static final int ECHO_VENDOR_EXTENSION_SERVICE_ID = 14;
  // Due multi-display, we can have multiple video and input services.
  // New service should add their SERVICE_ID above, and update the service ID below accordingly.
  private final AtomicInteger nextDisplayServiceId = new AtomicInteger(15);

    public static final String USB_DISCONNECT_INTENT = "usb-disconnect";

    // Not very OO, but makes calls look nicer.
    public GalReceiver galReceiver;
    public AudioSink[] audioSinks = new AudioSink[NUM_AUDIO_SINKS];
    public AudioSource audioSource;
  public InputSource mainDisplayInputSource;
    public BluetoothEndpoint bluetooth;
    public WifiProjectionEndpoint wifiProjEndpoint;
    public SensorSource sensors;
  public VideoSink mainDisplayVideoSink;
    public NavigationStatus navigationStatus;
    public MediaPlaybackStatus mediaPlaybackStatus;
    public MediaBrowser mediaBrowse;
    public PhoneStatus phoneStatus;
    public GalVerificationVendorExtension galVerificationVendorExtension;
    public EchoVendorExtension echoVendorExtension;
    public RadioEndpoint radioEndpoint;
  public Map<Integer, Display> displayIdDisplayMap = new HashMap<>();
  // MAIN display must have display ID 0;
  public static final int MAIN_DISPLAY_ID = 0;

  // Each display's VideoSink and InputSource services require a matching display ID.
  // After adding a display, this ID will be incremented for the next display.
  private final AtomicInteger currentDisplayId = new AtomicInteger(1);
    private View mSurfaceView;

    public boolean init(
            String carBluetoothAddress,
            int[] supportedBluetoothPairingMethods,
            int[] supportedSensors,
            SurfaceView surfaceView,
            VideoSink.ProjectionListener projectionListener,
            boolean autoStartProjection,
            AudioSink.AudioListener[] audioListeners,
            AudioSource.AudioSourceListener audioSourceListener,
            GalReceiver.AppMessageListener appMessageListener,
            GalReceiver.AudioFocusListener audioFocusListener,
            GalReceiver.ProjectedModeIconReceiver projectedModeIconReceiver,
            GalReceiver.ByeByeHandler byeByeHandler,
            GalReceiver.UserSwitchListener userSwitchListener,
            BluetoothEndpoint.BluetoothEventListener bluetoothEventListener,
            InstrumentClusterListener instrumentClusterListener,
            AudioFocusManager audioFocusManager,
            GalReceiver.CarInfo carInfo,
            Radio radio,
            CarWifi carWifi,
            Handler handler,
            String rootCert,
            String clientCert,
            String privateKey,
            String carWifiBssid) {
        mSurfaceView = surfaceView;

        galReceiver = new GalReceiver(appMessageListener, audioFocusListener,
                projectedModeIconReceiver, byeByeHandler, handler, carInfo, rootCert, clientCert,
                privateKey);

    mainDisplayInputSource = new InputSource(new DefaultInputInjector(MAIN_DISPLAY_ID));
        sensors = new SensorSource(supportedSensors,
                SensorSource.LOCATION_CHARACTERIZATION_RAW_GPS_ONLY,
                new Protos.FuelType[] { Protos.FuelType.FUEL_TYPE_UNLEADED,
                        FuelType.FUEL_TYPE_ELECTRIC },
                new Protos.EvConnectorType[] { EvConnectorType.EV_CONNECTOR_TYPE_CHADEMO,
                        EvConnectorType.EV_CONNECTOR_TYPE_J1772 });
    mainDisplayVideoSink = new VideoSink(projectionListener, autoStartProjection, 0);
    Display mainDisplay =
        new Display(
            DisplayType.DISPLAY_TYPE_MAIN,
            mainDisplayVideoSink,
            mainDisplayInputSource,
            MAIN_DISPLAY_ID);
    displayIdDisplayMap.put(MAIN_DISPLAY_ID, mainDisplay);

    for (Display display : displayIdDisplayMap.values()) {
      galReceiver.registerCarService(
          nextDisplayServiceId.getAndIncrement(), display.getVideoSink());
      galReceiver.registerCarService(
          nextDisplayServiceId.getAndIncrement(), display.getInputSource());
    }

        for (int i = 0; i < audioSinks.length; i++) {
            audioSinks[i] = new AudioSink(audioListeners[i]);
        }
        audioSource = new AudioSource(MediaCodecType.MEDIA_CODEC_AUDIO_PCM,
                audioSourceListener);
        bluetooth = new BluetoothEndpoint(bluetoothEventListener, carBluetoothAddress,
                supportedBluetoothPairingMethods);
        navigationStatus = new NavigationStatus(instrumentClusterListener,
                500, NavigationStatusService.InstrumentClusterType.IMAGE, 480, 480, 16);
        mediaPlaybackStatus = new MediaPlaybackStatus(instrumentClusterListener);
        mediaBrowse = new MediaBrowser(instrumentClusterListener);
        phoneStatus = new PhoneStatus(instrumentClusterListener);
    galVerificationVendorExtension =
        new GalVerificationVendorExtension(
            sensors, mainDisplayVideoSink, mainDisplayInputSource, surfaceView, audioFocusManager);
        echoVendorExtension = new EchoVendorExtension();
        galReceiver.registerCarService(SENSOR_SERVICE_ID, sensors);
        galReceiver.registerCarService(BLUETOOTH_SERVICE_ID, bluetooth);
        galReceiver.registerCarService(AUDIO_SERVICE_MEDIA_ID, audioSinks[AUDIO_SINK_INDEX_MEDIA]);
        galReceiver.registerCarService(AUDIO_SERVICE_TTS_ID, audioSinks[AUDIO_SINK_INDEX_TTS]);
        galReceiver.registerCarService(AUDIO_SERVICE_SYSTEM_ID, audioSinks[AUDIO_SINK_INDEX_SYSTEM]);
        galReceiver.registerCarService(AUDIO_SOURCE_SERVICE_ID, audioSource);
        galReceiver.registerCarService(NAVIGATION_STATUS_SERVICE_ID, navigationStatus);
        galReceiver.registerCarService(MEDIA_PLAYBACK_STATUS_SERVICE_ID, mediaPlaybackStatus);
        galReceiver.registerCarService(MEDIA_BROWSE_SERVICE_ID, mediaBrowse);
        galReceiver.registerCarService(PHONE_STATUS_SERVICE_ID, phoneStatus);
        // Only initialize WifiProjectionEndpoint if wifi is available in this car.
        if (carWifiBssid != null) {
            wifiProjEndpoint = new WifiProjectionEndpoint(carWifiBssid, carWifi);
            galReceiver.registerCarService(WIFI_PROJECTION_SERVICE_ID, wifiProjEndpoint);
        }
        if (radio != null) {
            radioEndpoint = new RadioEndpoint(radio);
            radio.setRadioEndpoint(radioEndpoint);
            galReceiver.registerCarService(RADIO_SERVICE_ID, radioEndpoint);
        }

        // For test purposes.
        galReceiver.registerCarService(
                GAL_VERIFICATION_VENDOR_EXTENSION_SERVICE_ID, galVerificationVendorExtension);
        galReceiver.registerCarService(ECHO_VENDOR_EXTENSION_SERVICE_ID, echoVendorExtension);

        return true;
    }

    public void start(Transport transport) {
        galReceiver.start(transport);
    }

    public void stop(Transport.StopReason reason) {
        galReceiver.stop(reason);
    }

    public void destroy() {
        galReceiver.destroy();
    }

  public int getCurrentDisplayId() {
    return currentDisplayId.get();
  }

  /**
   * This method should only be called from {@link
   * com.google.android.car.tests.carservice.CarEmulator#addDisplay}.
   */
  public InputInjector createDefaultInputInjector() {
    return new DefaultInputInjector(currentDisplayId.get());
  }

  /** All non-Main displays have to be added before calling {@link #init} */
  public void addDisplay(DisplayType displayType, VideoSink videoSink, InputSource inputSource) {
    Display display =
        new Display(displayType, videoSink, inputSource, currentDisplayId.getAndIncrement());
    displayIdDisplayMap.put(display.getDisplayId(), display);
  }

  /** Class to hold the display's necessary information. */
  public static class Display {
    private final int displayId;
    private final VideoSink videoSink;
    private final InputSource inputSource;
    private final DisplayType displayType;
    private KeyCode keyCode;

    public Display(
        DisplayType displayType, VideoSink videoSink, InputSource inputSource, int displayId) {
      this.displayId = displayId;
      this.displayType = displayType;
      this.videoSink = videoSink;
      this.inputSource = inputSource;
    }

    public void setUpVideoSinkProperties() {
      videoSink.setCodecType(MediaCodecType.MEDIA_CODEC_VIDEO_H264_BP);
      videoSink.setDisplayIdAndType(displayId, displayType);
      if (keyCode != null) {
        videoSink.setInitialContentKeyCode(keyCode);
      }
    }

    public void setUpInputSourceProperties() {
      inputSource.setDisplayId(displayId);
    }

    public void setInitialContentKeyCode(KeyCode keyCode) {
      this.keyCode = keyCode;
    }

    public DisplayType getDisplayType() {
      return displayType;
    }

    public int getDisplayId() {
      return displayId;
    }

    public InputSource getInputSource() {
      return inputSource;
    }

    public VideoSink getVideoSink() {
      return videoSink;
    }
  }

  private class DefaultInputInjector implements InputSource.InputInjector {
    private final int displayIndex;

    public DefaultInputInjector(int displayIndex) {
      this.displayIndex = displayIndex;
    }

    @Override
    public void onMotionEvent(MotionEvent event) {
      mSurfaceView.dispatchTouchEvent(event);
    }

    @Override
    public void onKeyEvent(KeyEvent event) {
      mSurfaceView.dispatchKeyEvent(event);
    }

    @Override
    public void onRelativeEvent(int keyCode, int delta) {
      displayIdDisplayMap.get(displayIndex).getInputSource().sendRelativeEvent(keyCode, delta);
    }
  }
}

// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.video;

import android.graphics.Rect;
import android.media.MediaCodec;
import android.media.MediaCodec.BufferInfo;
import android.media.MediaFormat;
import android.os.Process;
import android.os.Trace;
import android.util.Log;
import android.view.MotionEvent;
import android.view.Surface;
import android.view.SurfaceHolder;
import android.view.SurfaceView;
import android.view.ViewGroup;
import android.widget.TextView;

import androidx.annotation.NonNull;

import com.google.android.projection.common.BufferPool;
import com.google.android.projection.protocol.AndroidHeadUnitLogging;
import com.google.android.projection.protocol.VideoFrame;
import com.google.android.projection.sink.audio.AudioLatencyChecker;

import java.nio.ByteBuffer;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

/**
 * Takes encoded H.264 video and renders it to a SurfaceView.
 */
public final class ProjectionRenderer {
    /**
     * Listener to be told about the state of the surface to which the
     * projection is being rendered.
     */
    public interface Listener {
        void onSurfaceCreated(int width, int height, boolean layoutChanged);

        void onSurfaceDestroyed(boolean layoutChanged);

        void onFrameRendered(int sessionId);
    }

    private static final String TAG = AndroidHeadUnitLogging.TAG_VIDEO;
    private static final boolean ENABLE_DEBUG_LOG = false;
    private static final boolean DBG_TRACE = true;
    private static final int RENDERER_STATE_NOT_STARTED = 1;
    private static final int RENDERER_STATE_RUNNING = 2;
    private static final int RENDERER_STATE_STOPPED = 3;

    private ByteBuffer[] mInputBuffers;

    private MediaCodec mCodec;
    private DecodeInputThread mInputThread;
    private DecodeOutputThread mOutputThread;

    // Codec 操作线程，用于线程安全的 codec 操作
    private CodecOperationThread mCodecOperationThread;

    // 跟踪当前 codec 的配置状态
    private Rect mCurrentCodecVideoSize;
    private Rect mCurrentCodecSurfaceFrame;

    private final SurfaceView mSurfaceView;
    private final Listener mListener;

    // The media codec is started iff all of these are true.
    private volatile int mState = RENDERER_STATE_NOT_STARTED;
    private Surface mSurface;
    private final Rect mSurfaceFrame = new Rect();
    private Rect mVideoSize;
    private final DimensionUtils.ScreenSettings mEmulatedScreen;
    private float mTouchXScale = 1.0f;
    private float mTouchYScale = 1.0f;
    private boolean mLayoutChangeOngoing = false;

    private final FrameCounter mFrameCounter;

    // Make sure we don't drop any packets, even when the codec hasn't been
    // started yet.
    // This is because the first few packets contain important configuration
    // information.
    private final LinkedBlockingQueue<VideoFrame> mPendingInputBuffers =
            new LinkedBlockingQueue<VideoFrame>();

    /**
     * buffer to store frames which were queued for decoding
     */
    private final LinkedBlockingQueue<VideoFrame> mQueuedInputBuffers =
            new LinkedBlockingQueue<VideoFrame>();

    private final VideoLatencyChecker mVideoLatencyChecker;
    private final AudioLatencyChecker mAudioLatencyChecker;

    /**
     * The renderer may behave in a broken fashion if the SurfaceView is already
     * initialised (ie. SurfaceHolder.Callback.surfaceChanged has already been
     * called).
     *
     * @param surfaceView    The view to which to render output. Not null.
     * @param fpsTextView    Where to display live frame rate information. Can be
     *                       null, in which case no FPS will be shown.
     * @param listener       The listener to be told when the surface is created and
     *                       destroyed.
     * @param emulatedScreen Parameters required to resize video according to
     *                       emulated hardware.
     */
    public ProjectionRenderer(SurfaceView surfaceView, TextView fpsTextView, Listener listener,
                              DimensionUtils.ScreenSettings emulatedScreen, VideoLatencyChecker latencyChecker,
                              AudioLatencyChecker audioLatencyChecker) {
        this.mSurfaceView = surfaceView;
        this.mListener = listener;
        this.mEmulatedScreen = emulatedScreen;


        if (fpsTextView != null) {
            mFrameCounter = new FrameCounter(fpsTextView);
        } else {
            mFrameCounter = null;
        }

        SurfaceHolder holder = surfaceView.getHolder();
        Surface s = holder.getSurface();
        if (s != null) {
            /*
             * This is ugly, but, we must do it this way. Connecting via WiFi direct, the
             * ProjectionRenderer is created sometime after the MainActivity is created.
             * In that case, the surfaceChanged callback is never invoked [the callback is
             * added long after the callback would have been invoked].
             */
            Rect frame = holder.getSurfaceFrame();
            updateSurface(s, frame.width(), frame.height());
        }
        holder.addCallback(mSurfaceCallback);
        mVideoLatencyChecker = latencyChecker;
        mAudioLatencyChecker = audioLatencyChecker;

        // 初始化 Codec 操作线程
        mCodecOperationThread = new CodecOperationThread();
        mCodecOperationThread.start();
    }

    /**
     * Must be called when you are no longer using the renderer. Normally, you
     * will create a ProjectionRenderer in Activity.onCreate(Bundle) and call
     * release() in Activity.onDestroy().
     */
    public synchronized void release() {
        if (mSurface != null) {
            mSurface = null;
            mListener.onSurfaceDestroyed(false);
        }
        mSurfaceView.getHolder().removeCallback(mSurfaceCallback);
        stop();

        // 关闭 Codec 操作线程
        if (mCodecOperationThread != null) {
            mCodecOperationThread.shutdown();
            mCodecOperationThread = null;
        }
    }

    /**
     * Tells renderer that encoding should start soon.
     * Actual starting should be done in start as it will be called in main thread context.
     */
    public synchronized void prepareStart(int width, int height) {
        mVideoSize = new Rect(0, 0, width, height);
        mState = RENDERER_STATE_RUNNING;
    }

    /**
     * Tells the renderer that we want it running. This doesn't necessarily
     * actually begin rendering; it might need to wait for the surface to be
     * ready. If the renderer is already started, this method has no effect.
     */
    public synchronized void start() {
        if (mSurfaceFrame.width() > 0 && mSurfaceFrame.height() > 0) {
            updateCodec();
        }
    }

    /**
     * Tells the renderer that we don't want it running any more. This stops
     * rendering if it is currently running. If the renderer is not already
     * started, this method has no effect.
     */
    public synchronized void stop() {
        mState = RENDERER_STATE_STOPPED;
        mPendingInputBuffers.clear();
        mQueuedInputBuffers.clear();
        updateCodec();
    }

    /**
     * @return True iff the renderer is currently started (ie. we are in a
     * start()/stop() pair).
     */
    public boolean isStarted() {
        return mState == RENDERER_STATE_RUNNING;
    }

    /**
     * @return The frame that will be rendered. Callers must NOT modify the returned structure.
     */
    public Rect getRenderFrame() {
        return mSurfaceFrame;
    }

    private long mLastFrameTime = System.currentTimeMillis();

    /**
     * The renderer must be started to call this method.
     *
     * @param frame A frame containing an input buffer for the decoder. Not null.
     */
    public synchronized void decodeBuffer(VideoFrame frame) {
        if (frame == null) {
            throw new IllegalArgumentException();
        }
        if (mState == RENDERER_STATE_NOT_STARTED) {
            throw new IllegalStateException();
        }
        if (mState == RENDERER_STATE_STOPPED) {
            Log.i(TAG, "Received frame when shutting down, ignoring.");
            return;
        }
//        long currentTime = System.currentTimeMillis();
//        long step = currentTime - mLastFrameTime;
//        mLastFrameTime = currentTime;
//        Log.e(TAG, "decodeBuffer:" + step + " size:" + frame.data.array().length + " current time:" + frame.timestamp);
//        mListener.onFrameRendered(frame.sessionId);
        mPendingInputBuffers.add(frame);
        if (ENABLE_DEBUG_LOG) {
            Log.d(TAG, "pending frames " + mPendingInputBuffers.size());
        }
    }

    public synchronized void handleCodecConfig(byte[] data) {
        if (mState == RENDERER_STATE_NOT_STARTED) {
            throw new IllegalStateException();
        }
        if (mState == RENDERER_STATE_STOPPED) {
            Log.i(TAG, "Received codec config while shutting down. Ignoring.");
            return;
        }
        ByteBuffer buffer = ByteBuffer.allocate(data.length);
        buffer.put(data);
        VideoFrame frame = new VideoFrame(0, 0, buffer);
        mPendingInputBuffers.add(frame);
    }

    private boolean codecShouldBeRunning() {
        return mState == RENDERER_STATE_RUNNING && mSurface != null;
    }

    /**
     * Updates the surface used to draw the projection. This may be called multiple times
     * if.
     */
    private void updateSurface(Surface surface, int width, int height) {
        if (mSurface != null) {
            mListener.onSurfaceDestroyed(mLayoutChangeOngoing);
        }

        mSurface = surface;
        mSurfaceFrame.set(0, 0, width, height);
        if (width > 0 && height > 0) {
            updateCodec();
        }

        mListener.onSurfaceCreated(width, height, mLayoutChangeOngoing);
    }

    /**
     * Starts or stops the media codec as appropriate.
     */
    private void updateCodec() {
        if (mCodec == null) {
            if (codecShouldBeRunning()) {
                // 计算触摸缩放比例
                mTouchXScale = mVideoSize.right / (float) mSurfaceFrame.right;
                mTouchYScale = mVideoSize.bottom / (float) mSurfaceFrame.bottom;

                // 使用 Codec 操作线程创建 codec
                mCodecOperationThread.createCodec();
            }
        } else {
            if (codecShouldBeRunning()) {
                // 检查是否需要更新 codec 配置
                boolean needsUpdate = false;

                if (mCurrentCodecVideoSize == null || !mCurrentCodecVideoSize.equals(mVideoSize)) {
                    Log.d(TAG, "Video size changed: " + mCurrentCodecVideoSize + " -> " + mVideoSize);
                    needsUpdate = true;
                }

                if (mCurrentCodecSurfaceFrame == null || !mCurrentCodecSurfaceFrame.equals(mSurfaceFrame)) {
                    Log.d(TAG, "Surface frame changed: " + mCurrentCodecSurfaceFrame + " -> " + mSurfaceFrame);
                    needsUpdate = true;
                }

                if (needsUpdate) {
                    // 计算触摸缩放比例
                    mTouchXScale = mVideoSize.right / (float) mSurfaceFrame.right;
                    mTouchYScale = mVideoSize.bottom / (float) mSurfaceFrame.bottom;

                    // 使用 Codec 操作线程更新 codec
                    mCodecOperationThread.updateCodec();
                } else {
                    Log.d(TAG, "Codec configuration unchanged, skipping update");
                }
            } else {
                // 使用 Codec 操作线程销毁 codec
                mCodecOperationThread.destroyCodec();
            }
        }
    }

    private void ackFrame() {
        VideoFrame frame = mQueuedInputBuffers.poll();
        if (frame != null) {
            mListener.onFrameRendered(frame.sessionId);
        }
    }

    private static final int DEQUEUE_BUFFER_WAIT_TIME_US = 10000;
    private static final int DEQUEUE_BUFFER_FAIL_MAX_RETRY = 10;

    private final class DecodeInputThread extends Thread {
        private boolean mShutdownThread;

        public DecodeInputThread() {
            super("ProjectionRenderer.DecodeInputThread");
        }

        @Override
        public void run() {
            VideoFrame frame = null;
            boolean codecConfigPassed = false;
            while (true) {
                synchronized (this) {
                    if (mShutdownThread) {
                        return;
                    }
                }

                if (frame == null) {
                    try {
                        frame = mPendingInputBuffers.poll(200, TimeUnit.MILLISECONDS);
                    } catch (InterruptedException e) {
                        synchronized (this) {
                            if (mShutdownThread) {
                                return;
                            }
                        }
                        throw new RuntimeException(e);
                    }
                    if (frame == null) {
                        continue;
                    }
                }
                ByteBuffer buffer = frame.data;
                int writeOffset = buffer.arrayOffset();
                int totalToWrite = buffer.limit();
                byte[] bufferArray = buffer.array();
                long presentationTimeUs = System.nanoTime() / 1000;
                if (mVideoLatencyChecker != null) {
                    mVideoLatencyChecker.onDecodingStart(totalToWrite, presentationTimeUs);
                }
                while (totalToWrite > 0) {
                    int dequeFails = 0;
                    boolean dequeFailed = false;
                    int inputBufferIndex;
                    if (DBG_TRACE) {
                        Trace.beginSection("dequeueInputBuffer");
                    }
                    while (true) {
                        inputBufferIndex =
                                mCodec.dequeueInputBuffer(DEQUEUE_BUFFER_WAIT_TIME_US);
                        if (inputBufferIndex == -1) {
                            dequeFails++;
                            if (dequeFails > DEQUEUE_BUFFER_FAIL_MAX_RETRY) {
                                Log.e(TAG, "dequeueInputBuffer failed, lost a frame.");
                                dequeFailed = true;
                                ackFrame();
                                break;
                            }
                        } else {
                            break;
                        }
                    }
                    if (DBG_TRACE) {
                        Trace.endSection();
                    }
                    if (dequeFailed) {
                        break;
                    }
                    ByteBuffer inputBuffer = mInputBuffers[inputBufferIndex];
                    int toWrite = Math.min(inputBuffer.capacity(), totalToWrite);
                    inputBuffer.rewind();
                    inputBuffer.put(bufferArray, writeOffset, toWrite);

                    if (DBG_TRACE) {
                        Trace.beginSection("queueInputBuffer");
                    }
                    mCodec.queueInputBuffer(inputBufferIndex, 0, inputBuffer.position(),
                            presentationTimeUs,
                            codecConfigPassed ? 0 : MediaCodec.BUFFER_FLAG_CODEC_CONFIG);
                    if (DBG_TRACE) {
                        Trace.endSection();
                    }
                    if (!codecConfigPassed) {
                        // codec config does not generate output frame
                        ackFrame();
                        codecConfigPassed = true;
                    }
                    totalToWrite -= toWrite;
                    writeOffset += toWrite;
                }
                if (totalToWrite == 0) { // can reach here after dequesInputBuffer fail
                    if (ENABLE_DEBUG_LOG) {
                        Log.d(TAG, "queued input buffer pending " + mQueuedInputBuffers.size() +
                                " timestamp " + presentationTimeUs);
                    }
                    mQueuedInputBuffers.add(frame);
                }
                BufferPool.returnBuffer(frame.data);
                frame.data = null;
                frame = null;
            }
        }

        public void shutdown() {
            synchronized (this) {
                mShutdownThread = true;
            }

            try {
                join();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private final class DecodeOutputThread extends Thread {
        private boolean mShutdownThread;

        public DecodeOutputThread() {
            super("ProjectionRenderer.DecodeOutputThread");
        }

        @Override
        public void run() {
            BufferInfo info = new BufferInfo();
            while (true) {
                synchronized (this) {
                    if (mShutdownThread) {
                        return;
                    }
                }
                int outputBufferIndex = mCodec.dequeueOutputBuffer(info, 1000);
                if (outputBufferIndex == MediaCodec.INFO_OUTPUT_BUFFERS_CHANGED) {
                    continue;
                } else if (outputBufferIndex < 0) {
                    continue;
                } else {
                    ackFrame();
                    if (ENABLE_DEBUG_LOG) {
                        Log.d(TAG, String.format("Frame decoded timestamp: %d",
                                info.presentationTimeUs));
                    }
                    if (mVideoLatencyChecker != null) {
                        mVideoLatencyChecker.onDecodingFinish(info.presentationTimeUs);
                    }
                    if (mFrameCounter != null) {
                        long latestVideoLatency = 0;
                        long latestAudioLatency = 0;
                        if (mVideoLatencyChecker != null) {
                            latestVideoLatency = mVideoLatencyChecker.getLatestLatency();
                        }
                        if (mAudioLatencyChecker != null) {
                            latestAudioLatency = mAudioLatencyChecker.getLatestLatency();
                        }
                        mFrameCounter.onRenderFrame(latestVideoLatency, latestAudioLatency);
                    }
                    mCodec.releaseOutputBuffer(outputBufferIndex, true);
                }
            }
        }

        public void shutdown() {
            synchronized (this) {
                mShutdownThread = true;
            }

            try {
                join();
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
    }

    private final SurfaceHolder.Callback mSurfaceCallback = new SurfaceHolder.Callback() {
        @Override
        public void surfaceCreated(@NonNull SurfaceHolder holder) {
        }

        @Override
        public void surfaceChanged(SurfaceHolder holder, int format, int width, int height) {
            Log.i(TAG, "surfaceChanged, layout changed " + mLayoutChangeOngoing);
            synchronized (ProjectionRenderer.this) {
                updateSurface(holder.getSurface(), width, height);
            }
        }

        @Override
        public void surfaceDestroyed(SurfaceHolder holder) {
            synchronized (ProjectionRenderer.this) {
                mListener.onSurfaceDestroyed(false);

                mSurface = null;
                updateCodec();
            }
        }
    };

    /**
     * Transforms event coordinates to 'video'-relative
     */
    @SuppressWarnings("unused")
    public MotionEvent transformMotion(MotionEvent event) {
        int pointerCount = event.getPointerCount();
        MotionEvent.PointerProperties[] pp = new MotionEvent.PointerProperties[pointerCount];
        MotionEvent.PointerCoords[] pc = new MotionEvent.PointerCoords[pointerCount];

        for (int i = 0; i < pointerCount; ++i) {
            pp[i] = new MotionEvent.PointerProperties();
            event.getPointerProperties(i, pp[i]);
            pc[i] = new MotionEvent.PointerCoords();
            event.getPointerCoords(i, pc[i]);
            pc[i].x *= mTouchXScale;
            pc[i].y *= mTouchYScale;
            pc[i].x = Math.min(mSurfaceFrame.right, Math.max(pc[i].x, 0.0f));
            pc[i].y = Math.min(mSurfaceFrame.bottom, Math.max(pc[i].y, 0.0f));
            if (ENABLE_DEBUG_LOG && (i == 0)) {
                Log.d(TAG, "send touch " + pc[i].x + "," + pc[i].y + "scale " + mTouchXScale + "," +
                        mTouchYScale);
            }
        }
        return MotionEvent.obtain(
                event.getDownTime(),
                event.getEventTime(),
                event.getAction(),
                pointerCount, pp, pc,
                event.getMetaState(),
                event.getButtonState(),
                event.getXPrecision(),
                event.getYPrecision(),
                event.getDeviceId(),
                event.getEdgeFlags(),
                event.getSource(),
                event.getFlags());
    }

    /**
     * 专门处理 Codec 操作的线程，确保线程安全
     */
    private final class CodecOperationThread extends Thread {
        private static final int OP_CREATE_CODEC = 1;
        private static final int OP_UPDATE_CODEC = 2;
        private static final int OP_DESTROY_CODEC = 3;
        private static final int OP_SHUTDOWN = 4;

        private final Object mLock = new Object();
        private final LinkedBlockingQueue<Integer> mOperationQueue = new LinkedBlockingQueue<>();
        private volatile boolean mShutdown = false;

        public CodecOperationThread() {
            super("ProjectionRenderer.CodecOperationThread");
        }

        @Override
        public void run() {
            while (!mShutdown) {
                try {
                    Integer operation = mOperationQueue.poll(1000, TimeUnit.MILLISECONDS);
                    if (operation == null) {
                        continue;
                    }

                    switch (operation) {
                        case OP_CREATE_CODEC:
                            handleCreateCodec();
                            break;
                        case OP_UPDATE_CODEC:
                            handleUpdateCodec();
                            break;
                        case OP_DESTROY_CODEC:
                            handleDestroyCodec();
                            break;
                        case OP_SHUTDOWN:
                            mShutdown = true;
                            break;
                    }
                } catch (InterruptedException e) {
                    if (mShutdown) {
                        break;
                    }
                    Thread.currentThread().interrupt();
                    break;
                }
            }
        }

        public void createCodec() {
            if (!mShutdown) {
                mOperationQueue.offer(OP_CREATE_CODEC);
            }
        }

        public void updateCodec() {
            if (!mShutdown) {
                mOperationQueue.offer(OP_UPDATE_CODEC);
            }
        }

        public void destroyCodec() {
            if (!mShutdown) {
                mOperationQueue.offer(OP_DESTROY_CODEC);
            }
        }

        public void shutdown() {
            mOperationQueue.offer(OP_SHUTDOWN);
            try {
                join(5000); // 等待最多5秒
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

        private void handleCreateCodec() {
            synchronized (ProjectionRenderer.this) {
                // 如果已经有 codec，不需要重新创建
                if (mCodec != null) {
                    Log.d(TAG, "Codec already exists, skipping creation");
                    return;
                }

                try {
                    Log.d(TAG, "Creating new codec for video " + mVideoSize);

                    // 创建 MediaCodec
                    mCodec = MediaCodec.createDecoderByType("video/avc");

                    // 配置 codec
                    if (configureCodec()) {
                        // 启动解码线程
                        mOutputThread = new DecodeOutputThread();
                        mOutputThread.start();

                        mInputThread = new DecodeInputThread();
                        mInputThread.start();

                        // 更新状态跟踪
                        mCurrentCodecVideoSize = new Rect(mVideoSize);
                        mCurrentCodecSurfaceFrame = new Rect(mSurfaceFrame);
                    } else {
                        // 配置未完成，需要等待布局更新，先释放 codec
                        if (mCodec != null) {
                            mCodec.release();
                            mCodec = null;
                        }
                        mInputBuffers = null;
                    }

                } catch (Exception e) {
                    Log.e(TAG, "Failed to create codec", e);
                    // 清理状态
                    if (mCodec != null) {
                        try {
                            mCodec.release();
                        } catch (Exception ex) {
                            Log.e(TAG, "Error releasing codec after creation failure", ex);
                        }
                        mCodec = null;
                    }
                    mInputBuffers = null;
                }
            }
        }

        private void handleUpdateCodec() {
            synchronized (ProjectionRenderer.this) {
                if (mCodec == null) {
                    Log.w(TAG, "Cannot update codec - codec is null");
                    return;
                }

                try {
                    Log.d(TAG, "Updating codec configuration for video " + mVideoSize);

                    // 先停止解码线程
                    if (mInputThread != null) {
                        mInputThread.shutdown();
                        mInputThread = null;
                    }

                    if (mOutputThread != null) {
                        mOutputThread.shutdown();
                        mOutputThread = null;
                    }

                    // 停止当前 codec（但不释放）
                    mCodec.stop();

                    // 重新配置 codec
                    configureCodec();

                    // 重新启动解码线程
                    mOutputThread = new DecodeOutputThread();
                    mOutputThread.start();

                    mInputThread = new DecodeInputThread();
                    mInputThread.start();

                    // 更新状态跟踪
                    mCurrentCodecVideoSize = new Rect(mVideoSize);
                    mCurrentCodecSurfaceFrame = new Rect(mSurfaceFrame);

                } catch (Exception e) {
                    Log.e(TAG, "Failed to update codec", e);
                    // 如果更新失败，销毁 codec
                    handleDestroyCodec();
                }
            }
        }

        private boolean configureCodec() throws Exception {
            Rect viewport = DimensionUtils.makeViewport(
                    mSurfaceFrame.right, mSurfaceFrame.bottom, mVideoSize, mEmulatedScreen);
            Log.d(TAG, "Surface " + mSurfaceFrame
                    + " becomes " + viewport + " for video " + mVideoSize);

            if ((mSurfaceView.getWidth() != viewport.width()
                    || mSurfaceView.getHeight() != viewport.height())
                    && !mLayoutChangeOngoing) {
                // 需要在主线程中更新布局
                mSurfaceView.post(() -> {
                    ViewGroup.MarginLayoutParams layoutParams =
                            (ViewGroup.MarginLayoutParams) mSurfaceView.getLayoutParams();
                    layoutParams.width = viewport.width();
                    layoutParams.height = viewport.height();
                    layoutParams.setMargins(
                            layoutParams.leftMargin + viewport.left,
                            layoutParams.topMargin + viewport.top, 0, 0);
                    mSurfaceView.setLayoutParams(layoutParams);
                    Log.d(TAG, "layout change requested");
                    mLayoutChangeOngoing = true;
                });
                return false; // 表示配置未完成，需要等待布局更新
            }
            mLayoutChangeOngoing = false;

            MediaFormat format = MediaFormat.createVideoFormat("video/avc",
                    mVideoSize.width(), mVideoSize.height());
            Log.d(TAG, "Configure codec: frame "
                    + mSurfaceView.getHolder().getSurfaceFrame());
            mCodec.configure(format, mSurfaceView.getHolder().getSurface(), null, 0);
            mCodec.setVideoScalingMode(MediaCodec.VIDEO_SCALING_MODE_SCALE_TO_FIT);
            mCodec.start();
            mInputBuffers = mCodec.getInputBuffers();
            return true; // 表示配置成功完成
        }

        private void handleDestroyCodec() {
            synchronized (ProjectionRenderer.this) {
                if (mInputThread != null) {
                    mInputThread.shutdown();
                    mInputThread = null;
                }

                if (mOutputThread != null) {
                    mOutputThread.shutdown();
                    mOutputThread = null;
                }

                mInputBuffers = null;
                if (mCodec != null) {
                    try {
                        mCodec.stop();
                        mCodec.release();
                    } catch (Exception e) {
                        Log.e(TAG, "Error destroying codec", e);
                    } finally {
                        mCodec = null;
                    }
                }

                // 清理状态跟踪
                mCurrentCodecVideoSize = null;
                mCurrentCodecSurfaceFrame = null;
            }
        }
    }
}

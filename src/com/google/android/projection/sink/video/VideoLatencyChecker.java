// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.video;

import android.util.Log;
import android.view.MotionEvent;

import com.google.android.projection.protocol.AndroidHeadUnitLogging;

import java.util.ArrayList;

/**
 * Tool to check latency. To use this, MainActivity.DBG_LATENCY should be true.
 * Latency detection is based on the idea that when user input is delivered, there
 * will be increase in frame size. To measure latency, leave screen untouched for a while,
 * and send input event (single button press) to trigger screen update.
 * When LatencyChecker finds increase in frame size compared to the average of previous frames,
 * (with frame size bigger than threshold) it will consider that frame as a result of
 * the input event.
 */
public class VideoLatencyChecker {
    private static final String TAG = AndroidHeadUnitLogging.TAG_VIDEO;
    private static final int NUM_FRAMES_FOR_AVERAGE_SIZE = 2;
    // for debugging latency checker itself.
    private static final boolean DBG = false;
    /**
     * Frame size bigger than average frame size more them this time will be treated as a
     * frame to measure latency.
     */
    private static final int FRAME_SIZE_MULTIPLIER = 4;
    /**
     * Frame size should be bigger than this to be considered as a frame update
     */
    private static final int FRAME_SIZE_THRESHOLD = 4096;
    /**
     * It may be that latency above this is simply frame size not increasing that dramatically.
     * So latency bigger than this will not be shown.
     */
    private static final long LATENCY_IGNORE_ABOVE_THIS = 500;
    /**
     * once latency is checked, do not check it for certain time as subsequent frames can also
     * come with bigger data for a while.
     */
    private static final int NO_LATENCY_CHECK_PERIOD = 2;
    private final int[] mFrameSizes = new int[NUM_FRAMES_FOR_AVERAGE_SIZE];
    private int mCurrentFrameLocation = 0;
    private int mAverageFrameSize = 0;
    private int mFrameSize = 0;
    /**
     * Do not check latency while this is positive
     */
    private int mNoLatencyCheckCounter = 0;
    private long mInputEventTimeMsVideo = 0;
    private long mDecodingStartTimeMs;
    private long mPresentationTimeUs = 0;
    private boolean mVideoLatencyCheckOngoing = false;
    private long mLatestVideoLatency = 0;

    public synchronized void onInputEvent(MotionEvent event) {
        // ACTION_DOWN is ignored as typically it does not trigger UI action.
        // But ACTION_DOWN resets latency check.
        if (event.getAction() == MotionEvent.ACTION_DOWN) {
            if (DBG) {
                Log.d(TAG, "onInputEvent ACTION_DOWN " + mInputEventTimeMsVideo);
            }
            mVideoLatencyCheckOngoing = false;
            mNoLatencyCheckCounter = 0;
            return;
        }
        long inputEventTimeMs = System.currentTimeMillis();
        if (!mVideoLatencyCheckOngoing) {
            mInputEventTimeMsVideo = inputEventTimeMs;
            mVideoLatencyCheckOngoing = true;
            if (DBG) {
                Log.d(TAG, "onInputEvent start latency check " + mInputEventTimeMsVideo);
            }
        }
    }

    public synchronized void onDecodingStart(int frameSize, long presentationTimeUs) {
        if (DBG) {
            Log.d(TAG, "onDecodingStart " + frameSize + "," + presentationTimeUs);
        }
        if (mNoLatencyCheckCounter > 0) {
            if (DBG) {
                Log.d(TAG, "no latency check period");
            }
            mNoLatencyCheckCounter--;
            return;
        }
        // first condition is for not measuring latency for initial data
        if ((mAverageFrameSize > 0) && mVideoLatencyCheckOngoing &&
                (frameSize > mAverageFrameSize * FRAME_SIZE_MULTIPLIER) &&
                (frameSize > FRAME_SIZE_THRESHOLD)) {
            if (DBG) {
                Log.d(TAG, "big frame" + frameSize+" check latency start");
            }
            mDecodingStartTimeMs = System.currentTimeMillis();
            mPresentationTimeUs = presentationTimeUs;
            mNoLatencyCheckCounter = NO_LATENCY_CHECK_PERIOD;
            mFrameSize = frameSize;
        } else { // normal average update
            int oldSizeToGo = mFrameSizes[mCurrentFrameLocation];
            mAverageFrameSize += (frameSize - oldSizeToGo) / NUM_FRAMES_FOR_AVERAGE_SIZE;
            if (DBG) {
                Log.d(TAG, "normal frame, average size: " + mAverageFrameSize);
            }
            mFrameSizes[mCurrentFrameLocation] = frameSize;
            mCurrentFrameLocation++;
            if (mCurrentFrameLocation >= NUM_FRAMES_FOR_AVERAGE_SIZE) {
                mCurrentFrameLocation = 0;
            }
        }
    }

    public synchronized void onDecodingFinish(long presentationTimeUs) {
        if (DBG) {
            Log.d(TAG, "onDecodingFinish " + presentationTimeUs);
        }
        if (presentationTimeUs == mPresentationTimeUs) {
            long decodingFinishTimeMs = System.currentTimeMillis();
            mLatestVideoLatency = decodingFinishTimeMs - mInputEventTimeMsVideo;
            if (DBG) {
                Log.i(TAG, "**Latency measurement (ms) **" +
                        " Input event to encoded frame: " +
                        (mDecodingStartTimeMs - mInputEventTimeMsVideo) +
                        " Decoding latency: " + (decodingFinishTimeMs - mDecodingStartTimeMs) +
                        " Total latency: " + mLatestVideoLatency +
                        " Frame size: " + mFrameSize);
            }
            // reset to prevent latency check without input
            mVideoLatencyCheckOngoing = false;
            mInputEventTimeMsVideo = 0;
        }
    }

    public long getLatestLatency() {
        if (mLatestVideoLatency > LATENCY_IGNORE_ABOVE_THIS) {
            return 0;
        } else {
            return mLatestVideoLatency;
        }
    }
}

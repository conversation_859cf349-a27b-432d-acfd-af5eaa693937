// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.video;

import android.os.Handler;
import android.os.SystemClock;
import android.util.Log;
import android.widget.TextView;
import com.google.android.projection.protocol.AndroidHeadUnitLogging;

/**
 * Keeps track of frames being rendered for frame rate display/logging.
 */
public final class FrameCounter {
    private static final String TAG = AndroidHeadUnitLogging.TAG_VIDEO;
    private static final boolean DBG = false;

    /**
     * Avoid updating the FPS display more often than this, to keep it readable.
     */
    private static final int MIN_UPDATE_PERIOD_MS = 500;

    private final TextView mFpsTextView;
    private final StringBuffer mFpsStringBuffer = new StringBuffer();

    /**
     * When the last frame was rendered, or 0 if no frame has been rendered yet.
     */
    private long lastFrame;

    private long measureStartTime;
    private int numFramesMeasured;

    private final Handler handler = new Handler();

    /**
     * @param fpsTextView Not null.
     */
    public FrameCounter(TextView fpsTextView) {
        this.mFpsTextView = fpsTextView;
    }

    /**
     * This method may be called on some arbitrary thread (but always the same
     * thread).
     */
    public void onRenderFrame(long videoLatency, long audioLatency) {
        long now = SystemClock.elapsedRealtime();
        if (lastFrame != 0) {
            if (DBG) {
                Log.d(TAG, String.format("Time since last frame: %d ms", now - lastFrame));
            }
            long timeMeasured = now - measureStartTime;
            if (timeMeasured >= MIN_UPDATE_PERIOD_MS) {
                final String fpsText = mFpsStringBuffer
                        .append("FPS: ")
                        .append((float) (numFramesMeasured * 1000.0 / timeMeasured))
                        .append(", v-lat ")
                        .append(videoLatency)
                        .append(", a-lat ")
                        .append(audioLatency)
                        .toString();
                mFpsStringBuffer.delete(0, fpsText.length());
                handler.post(new Runnable() {
                    @Override
                    public void run() {
                        mFpsTextView.setText(fpsText);
                        mFpsTextView.invalidate();
                    }
                });
                numFramesMeasured = 0;
            }
        }
        if (numFramesMeasured == 0) {
            measureStartTime = now;
        }
        ++numFramesMeasured;
        lastFrame = now;
    }
}

目前的分析状况
从trace中目前看到的几点:
1.点击后的第一帧来得很慢
2.decoder 整体的解码速度很慢
3.上述地两个问题导致了上帧错过了最理想的那个vsync周期
4.内存使用很紧张

后续工作
1.要确定第一帧是什么时候来的（先不上帧，在touch后，存储后续来的画面帧）
2.抛弃他的touch的一套机制，通过模拟touch的方式，让手机发数据，检查是否因为touch的原因导致数据来得较慢
3.decoder硬件解码大概12ms，但是整个流程大概20几ms左右，需要对每个关键点，加上trace，来分析整个流程耗时的点



当前状况：
		确认各个环节的耗时，在关键点增加 trace，测试
			decoder时间=queueinputbuffer(2~3ms)+binder调度(1~2ms)+解码前置工作(~1ms)+件解码(11ms左右)+binder调度(1~2ms)+outputbuffer数据处理(2~3ms)
		模拟touch事件，看是否有改善
			无改善
		记录每一帧的数据查看该帧数据来得时间点（在touch之后）
			每一帧的数据可以看到，但是无法确认闪光灯的时间，虽然手机有log，因为两个设备的时钟不是完全同步的，拿不到精确的时间
		先不上帧，只保留解码后的数据，测试该段流程的时间
			即使不上帧，解码的数据一样很慢，所以跟是否上帧的关系不大
	
后续工作
    压榨性能
		绑定应用和decoder去大核（目前跑起来的时候大核几乎没工作）
		增加CPU的频率
		提高DDR的频率
		增加GPU的频率
        查看展锐的性能优化PDF，有没更一步的方法
// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.video;

import android.graphics.Rect;

/**
 * Helper utilities to manage conversion between video and screen dimensions.
 */
public class DimensionUtils {
    private DimensionUtils() {}

    public static Rect makeRequestedVideoSize(int surfaceWidth, int surfaceHeight,
            ScreenSettings settings) {
        if (settings.isNativeSize()) {
            return new Rect(0, 0, surfaceWidth, surfaceHeight);
        } else {
            return new Rect(0, 0, settings.getWidth(), settings.getHeight());
        }
    }

    public static Rect makeViewport(int surfaceWidth, int surfaceHeight, Rect videoSize,
            ScreenSettings settings) {
        float w = videoSize.right;
        float h = videoSize.bottom;
        if (settings.isScaled()) {
            float rescale = Math.min(surfaceWidth / w, surfaceHeight / h);
            w *= rescale;
            h *= rescale;
        }
        // align by two to make it centered
        int newWidth = (Math.round(w) / 2) * 2;
        int newHeight = (Math.round(h) / 2) * 2;

        // Make sure we don't exceed surface dimensions
        newWidth = Math.min(newWidth, surfaceWidth);
        newHeight = Math.min(newHeight, surfaceHeight);

        int extraWidth = surfaceWidth - newWidth;
        int extraHeight = surfaceHeight - newHeight;
        return new Rect(extraWidth / 2, extraHeight / 2,
                extraWidth / 2 + newWidth, extraHeight / 2 + newHeight);
    }

    /**
     * Holder of screen settings used to preserve correct projected image size.
     */
    public static class ScreenSettings {
        private final int mWidth;
        private final int mHeight;
        private final float mAdvertisedDpi; // The DPI that device announces to GMM
        private final float mPhysicalDpi;   // Real tablet's DPI
        private final boolean mScale;       // Resize to screen?

        public ScreenSettings(int width, int height,
                float advertisedDpi, float physicalDpi, boolean scale) {
            mWidth = width;
            mHeight = height;
            mAdvertisedDpi = advertisedDpi;
            mPhysicalDpi = physicalDpi;
            mScale = scale;
        }

        public final int getWidth() {
            return mWidth;
        }

        public final int getHeight() {
            return mHeight;
        }

        public final boolean isNativeSize() {
            return mWidth == 0 || mHeight == 0;
        }

        public final boolean isScaled() {
            return mScale;
        }

        public final float getEmulatedDpi() {
            return mAdvertisedDpi;
        }

        public final float getPhysicalDpi() {
            return mPhysicalDpi;
        }
    }
}

// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.ui;

import android.Manifest;
import android.annotation.SuppressLint;
import android.app.Activity;
import android.app.ActivityManager;
import android.app.AlertDialog;
import android.app.PendingIntent;
import android.bluetooth.BluetoothAdapter;
import android.bluetooth.BluetoothDevice;
import android.bluetooth.BluetoothGatt;
import android.bluetooth.BluetoothGattCallback;
import android.bluetooth.BluetoothProfile;
import android.bluetooth.BluetoothServerSocket;
import android.bluetooth.BluetoothSocket;
import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.DialogInterface;
import android.content.Intent;
import android.content.IntentFilter;
import android.content.SharedPreferences;
import android.content.pm.PackageInfo;
import android.content.pm.PackageManager;
import android.content.pm.PackageManager.NameNotFoundException;
import android.content.res.Configuration;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.Rect;
import android.graphics.drawable.BitmapDrawable;
import android.graphics.drawable.InsetDrawable;
import android.hardware.usb.UsbAccessory;
import android.hardware.usb.UsbDevice;
import android.hardware.usb.UsbManager;
import android.media.AudioFormat;
import android.media.AudioManager;
import android.net.wifi.WifiManager;
import android.os.Build;
import android.os.Bundle;
import android.os.Handler;
import android.os.Trace;
import android.os.Vibrator;
import android.util.DisplayMetrics;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.SurfaceView;
import android.view.View;
import android.view.View.OnTouchListener;
import android.view.ViewGroup;
import android.view.ViewTreeObserver;
import android.view.Window;
import android.view.WindowInsetsController;
import android.view.WindowManager;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.RelativeLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.RequiresApi;
import androidx.core.app.ActivityCompat;
import androidx.core.content.ContextCompat;
import androidx.core.view.WindowCompat;
import androidx.core.view.WindowInsetsCompat;
import androidx.core.view.WindowInsetsControllerCompat;
import androidx.localbroadcastmanager.content.LocalBroadcastManager;

import com.google.android.projection.proto.Protos;
import com.google.android.projection.proto.Protos.AudioStreamType;
import com.google.android.projection.proto.Protos.KeyCode;
import com.google.android.projection.proto.Protos.MediaCodecType;
import com.google.android.projection.proto.Protos.MessageStatus;
import com.google.android.projection.proto.Protos.VideoConfiguration;
import com.google.android.projection.protocol.AndroidHeadUnitLogging;
import com.google.android.projection.protocol.AudioSink;
import com.google.android.projection.protocol.BluetoothEndpoint;
import com.google.android.projection.protocol.CarWifiStub;
import com.google.android.projection.protocol.GalReceiver;
import com.google.android.projection.protocol.GalReceiver.ConnectedDevice;
import com.google.android.projection.protocol.InputSource;
import com.google.android.projection.protocol.RadioStub;
import com.google.android.projection.protocol.Transport;
import com.google.android.projection.protocol.Transport.StopReason;
import com.google.android.projection.protocol.VideoFrame;
import com.google.android.projection.protocol.VideoSink;
import com.google.android.projection.sink.GalIntegration;
import com.google.android.projection.sink.InstrumentClusterListener;
import com.google.android.projection.sink.LocalModeCallbacksImpl;
import com.google.android.projection.sink.R;
import com.google.android.projection.sink.audio.AudioFocusManager;
import com.google.android.projection.sink.audio.AudioLatencyChecker;
import com.google.android.projection.sink.audio.AudioPlayer;
import com.google.android.projection.sink.audio.MicrophoneRecorder;
import com.google.android.projection.sink.debug.DebugContext;
import com.google.android.projection.sink.navigation.NavigationFocusManager;
import com.google.android.projection.sink.sensors.FakeSensors;
import com.google.android.projection.sink.sensors.LocationSensors;
import com.google.android.projection.sink.sensors.SensorManager;
import com.google.android.projection.sink.sensors.SimpleObdSensor;
import com.google.android.projection.sink.sensors.SimulatedLocationSensors;
import com.google.android.projection.sink.sensors.TabletSensors;
import com.google.android.projection.sink.settings.Settings;
import com.google.android.projection.sink.transport.usb.UsbHostSetup;
import com.google.android.projection.sink.video.DimensionUtils;
import com.google.android.projection.sink.video.ProjectionRenderer;
import com.google.android.projection.sink.video.VideoLatencyChecker;
import com.google.android.projection.transport.ServerSocketProvider;
import com.google.android.projection.transport.TransportFactory;
import com.google.android.projection.transport.TransportFactory.FactoryHandle;

import java.io.ByteArrayOutputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.net.ServerSocket;
import java.net.SocketException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * Shows video projected from the phone, and sends touch events back.
 */
public final class MainActivity extends Activity implements TransportFactory.Callback {
    private static final String PHONE_SCREEN_OFF_MSG = "Please turn phone screen on.";
    private static final String ACTION_USB_ACCESSORY_PERMISSION =
            "com.google.android.projection.sink.transport.usb.ACTION_USB_ACCESSORY_PERMISSION";
    private static final String ACTION_USB_DEVICE_PERMISSION =
            "com.google.android.projection.sink.transport.usb.ACTION_USB_DEVICE_PERMISSION";
    private static final int AUDIO_CHANNELS_MEDIA = 2;
    private static final int AUDIO_BITS_MEDIA = 16;
    private static final int SAMPLING_RATE_MEDIA = 48000;
    private static final int AUDIO_CHANNELS_TTS = 1;
    private static final int AUDIO_BITS_TTS = 16;
    private static final int SAMPLING_RATE_TTS = 16000;
    private static final int AUDIO_CHANNELS_SYSTEM = 1;
    private static final int AUDIO_BITS_SYSTEM = 16;
    private static final int SAMPLING_RATE_SYSTEM = 16000;
    private static final String TAG = AndroidHeadUnitLogging.TAG_MAIN_ACTIVITY;
    private static final boolean DBG_LATENCY = true;
    /**
     * ideally this should be 0 but N7 tablet has long pipe-line :(
     */
    private static final int DECODER_ADDITIONAL_DEPTH = 3;

    public static final String LAUNCH_MODE = "launch_mode";
    public static final int USB_ACCESSORY_MODE = 0;
    public static final int USB_HOST_MODE = 1;
    public static final int LAUNCHER_MODE = 2;
    public static final int LOCAL_MODE = 3;
    public static final String SAVED_BT_KEY = "saved_bt";

    private final Object mLock = new Object();

    private String mSsid;
    private String mBssid;
    private String mPassword;
    private String mMACAddress;
    private String ipAddressString;
    private int mSecurityMode = -1;
    private int mAccessPointType = -1;

    private RelativeLayout mLayout;

    private Settings mSettings;

    private TransportFactory mTransportFactory;
    private FactoryHandle mTransportHandle;
    private Transport mTransport;
    private GalIntegration mGal; // protected by mLock.
    private AudioFocusManager mAudioFocusManager;
    private NavigationFocusManager mNavigationFocusManager;
    private ProjectionRenderer mProjectionRenderer;
    private FileOutputStream mFileOutputStream;
    private ByteArrayOutputStream mBufferStream;
    private byte[] codecConfigData = null; // ���� onCodecConfig ������
    private boolean hasWrittenHeader = false;
    private SurfaceView mSurfaceView;
    public int ret = 0;
    private int originalWidth = 0;
    private int originalHeight = 0;
    private View mConnectLayout;
    private SensorManager mSensorDispatcher;
    private DebugContext mDebugContext;


    private DimensionUtils.ScreenSettings mEmulatedScreen;

    private TestTools mTestTools;
    private int mLaunchMode;

    private boolean mLocalMode = false;
    private LocalModeCallbacks mLocalModeCallbacks;
    private BluetoothAdapter mBluetoothAdapter;
    BluetoothServerSocket mBluetoothSocket;
    BluetoothSocket socket;
    public Context context;
    private int mApState;
    ServerSocket serverSocket;
    private static final int REQUEST_FINE_LOCATION = 99;
    private static final UUID uuid = UUID.fromString("4de17a00-52cb-11e6-bdf4-0800200c9a66");
    private final Handler mHandler = new Handler();


    private final BroadcastReceiver mUsbDisconnectedReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            // You can end up with both the onDisconnected and this calling finish which
            // results in a noisy warning: "Duplicate finish request for ActivityRecord".
            if (!MainActivity.this.isFinishing()) {
                System.exit(0);
            }
        }
    };
    byte[] senddata = new byte[]{
            (byte) 0x00, 0x6F, (byte) 0x00, 0x04, 0x08, 0x04, 0x10, 0x02, 0x18, 0x00,
            0x20, (byte) 0xBC, 0x28, 0x20, (byte) 0xF1, 0x2C, 0x2A, 0x61,
            0x0A, 0x0B, 0x52, 0x6F, 0x6C, 0x6C, 0x73, 0x2D, 0x52, 0x6F,
            0x79, 0x63, 0x65, 0x12, 0x07, 0x50, 0x68, 0x61, 0x6E, 0x74,
            0x6F, 0x6D, 0x1A, 0x04, 0x32, 0x30, 0x31, 0x36, 0x22, 0x24,
            0x38, 0x64, 0x31, 0x31, 0x66, 0x62, 0x35, 0x37, 0x2D, 0x63,
            0x37, 0x63, 0x62, 0x2D, 0x34, 0x34, 0x63, 0x36, 0x2D, 0x38,
            0x35, 0x39, 0x39, 0x2D, 0x31, 0x39, 0x64, 0x37, 0x36, 0x33,
            0x39, 0x36, 0x39, 0x34, 0x33, 0x37, 0x2A, 0x09, 0x58, 0x58,
            0x43, 0x6F, 0x6D, 0x70, 0x61, 0x6E, 0x79, 0x32, 0x07, 0x58,
            0x58, 0x4D, 0x6F, 0x64, 0x65, 0x6C, 0x3A, 0x04, 0x58, 0x58,
            0x4F, 0x53, 0x42, 0x03, 0x30, 0x2E, 0x31
    };
    private byte[] senddata2 = new byte[]{
            (byte) 0x00, 0x12, (byte) 0x00, 0x01, 0x0A, 0x0B, 0x31, 0x39, 0x32, 0x2E,
            0x31, 0x36, 0x38, 0x2E, 0x30, 0x2E, 0x31, 0x10, (byte) 0xBD,
            0x44, 0x18, (byte) 0x00
    };
    private byte[] senddata3 = new byte[]{
            (byte) 0x00, 0x31, (byte) 0x00, 0x03, 0x0A, 0x0C, 0x47, 0x65,
            0x6D, 0x69, 0x6E, 0x69, 0x63, 0x33, 0x34, 0x32, 0x30, 0x66,
            0x12, 0x0A, 0x39, 0x38, 0x39, 0x30, 0x35, 0x62, 0x33, 0x62,
            0x62, 0x32, 0x1A, 0x11, 0x33, 0x34, 0x3A, 0x43, 0x33, 0x3A,
            0x34, 0x41, 0x3A, 0x30, 0x33, 0x3A, 0x43, 0x31, 0x3A, 0x32,
            0x38, 0x20, 0x08, 0x28, (byte) 0x00
    };


    public interface BluetoothStateListener {
        void onBluetoothStateChanged(boolean state);
    }

    private BroadcastReceiver mIntentControllerReceiver;
    private final VideoLatencyChecker mVideoLatencyChecker = new VideoLatencyChecker();
    private final AudioLatencyChecker mAudioLatencyChecker = new AudioLatencyChecker();
    private BroadcastReceiver mBluetoothStartBroadcastReceiver;
    private BluetoothGatt mBluetoothGatt;
    private BluetoothDevice connectedDevice = null;
    public static final int WIFI_AP_STATE_ENABLED = 13;
    private static final int LOCATION_PERMISSION_REQUEST_CODE = 1;
    private int frameIndex = 0;


    /**
     * LocalModeCallbacks
     */
    public interface LocalModeCallbacks {
        public boolean isLocalMode();

        public void startLocalMode(MainActivity activity);

        public String getRootCert();

        public String getClientCert();

        public String getPrivateKey();
    }


    @Override
    protected void onCreate(Bundle savedInstanceState) {
        Log.d(TAG, "onCreate");
        super.onCreate(savedInstanceState);
        context = getApplicationContext();
        if (!isTaskRoot()
                && getIntent().hasCategory(Intent.CATEGORY_LAUNCHER)
                && getIntent().getAction() != null
                && getIntent().getAction().equals(Intent.ACTION_MAIN)) {
            Log.d(TAG, "onCreate if");
            SharedPreferences prefs1 = getSharedPreferences("app_prefs", MODE_PRIVATE);
            SharedPreferences.Editor editor = prefs1.edit();
            editor.putBoolean("returningFromHome", true);
            editor.apply();
            finish();
            return;
        }

        if (ContextCompat.checkSelfPermission(this, Manifest.permission.ACCESS_FINE_LOCATION) != PackageManager.PERMISSION_GRANTED) {
            ActivityCompat.requestPermissions(this, new String[]{
                    Manifest.permission.ACCESS_FINE_LOCATION,
                    Manifest.permission.ACCESS_COARSE_LOCATION
            }, LOCATION_PERMISSION_REQUEST_CODE);
        }
        SharedPreferences prefs1 = getSharedPreferences("app_prefs", MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs1.edit();
        editor.putBoolean("returningFromHome", false);
        editor.apply();

        WifiManager wifiManager = (WifiManager) getApplicationContext().getSystemService(Context.WIFI_SERVICE);

        Log.d("zzzzfile", "getFilesDir: " + getFilesDir());
        IntentFilter intentFilter = new IntentFilter();
        intentFilter.addAction("android.net.wifi.WIFI_AP_STATE_CHANGED");
        intentFilter.addAction("android.net.conn.TETHER_STATE_CHANGED");
        getApplicationContext().registerReceiver(mapIpReceiver, intentFilter);


        IntentFilter filter = new IntentFilter();
        filter.addAction(BluetoothDevice.ACTION_ACL_CONNECTED);
        filter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECT_REQUESTED);
        filter.addAction(BluetoothDevice.ACTION_ACL_DISCONNECTED);
        this.registerReceiver(mReceiver, filter);
        mLocalModeCallbacks = new LocalModeCallbacksImpl();
        mLocalMode = mLocalModeCallbacks.isLocalMode();
        Log.d(TAG, "localMode: " + mLocalMode);

        mSettings = new Settings(this);
        Log.d(TAG, "mSettings getCarWifiPassword: " + mSettings.getCarWifiPassword());
        Log.d(TAG, "mSettings getCarWifiSsid: " + mSettings.getCarWifiSsid());

        mTransportFactory = TransportFactory.builder()
                .setContext(this)
                .build();
        final Intent intent = getIntent();
        Log.i(TAG, "Start with intent: " + intent);
        if (intent.getBooleanExtra("stop", false)) {
            System.exit(0);
            return;
        }
        String action = intent.getAction();
        mLaunchMode = intent.getIntExtra(LAUNCH_MODE, LAUNCHER_MODE);
        if (action != null) {
            if (action.equals(UsbManager.ACTION_USB_ACCESSORY_ATTACHED)) {
                mLaunchMode = USB_ACCESSORY_MODE;
                Log.d(TAG, "mLaunchMode: " + mLaunchMode + " " + "USB_ACCESSORY_MODE");
            } else if (action.equals(UsbManager.ACTION_USB_DEVICE_ATTACHED)) {
                mLaunchMode = USB_HOST_MODE;
                Log.d(TAG, "mLaunchMode: " + mLaunchMode + " " + "USB_HOST_MODE");
            } else if (mLocalMode) {
                mLaunchMode = LOCAL_MODE;
                Log.d(TAG, "mLaunchMode: " + mLaunchMode + " " + "LOCAL_MODE");
            }
        } else {
            Log.d(TAG, "action is null!!!");
        }

        mDebugContext = new DebugContext(this);

        initializeViews();
        initializeSensorServices();

        final ViewTreeObserver vto = findViewById(R.id.surface).getViewTreeObserver();
        vto.addOnGlobalLayoutListener(new ViewTreeObserver.OnGlobalLayoutListener() {
            @Override
            public void onGlobalLayout() {
                Log.d(TAG, "onGlobalLayout");
                findViewById(R.id.surface).getViewTreeObserver().removeOnGlobalLayoutListener(this);
                // It's GAL notification, not necessary USB disconnection.
                LocalBroadcastManager.getInstance(MainActivity.this).registerReceiver(
                        mUsbDisconnectedReceiver,
                        new IntentFilter(GalIntegration.USB_DISCONNECT_INTENT));

                Log.d(TAG, "mLaunchMode: " + mLaunchMode);
                switch (mLaunchMode) {
                    case USB_ACCESSORY_MODE: {
                        UsbAccessory accessory = (UsbAccessory)
                                intent.getParcelableExtra(UsbManager.EXTRA_ACCESSORY);
                        startAccessoryMode(accessory);
                        break;
                    }
                    case USB_HOST_MODE: {
                        UsbDevice device = (UsbDevice)
                                intent.getParcelableExtra(UsbManager.EXTRA_DEVICE);
                        startHostMode(device);
                        break;
                    }
                    case LAUNCHER_MODE: {
                        startTcpServerMode();
                        break;
                    }
                    case LOCAL_MODE: {
                        mLocalModeCallbacks.startLocalMode(MainActivity.this);
                        break;
                    }
                    default:
                        throw new RuntimeException("Unhandled case: " + mLaunchMode);
                }
            }
        });

        hideSystemUi();
    }


    private final BluetoothGattCallback gattCallback = new BluetoothGattCallback() {

        @Override
        public void onConnectionStateChange(BluetoothGatt gatt, int status, int newState) {
            if (newState == BluetoothProfile.STATE_CONNECTED) {
                connectedDevice = gatt.getDevice();
                Log.d(TAG, "connectedDevice: " + connectedDevice);
            }
        }
    };

    private final BroadcastReceiver mapIpReceiver = new BroadcastReceiver() {
        @RequiresApi(api = Build.VERSION_CODES.R)
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            Log.e(TAG, "Receiver action:" + action);
        }
    };

    @RequiresApi(api = Build.VERSION_CODES.S)
    @SuppressLint({"MissingPermission", "HardwareIds"})
    private void getWifiInfo() {
        //ipAddressString = "192.168.266.124";
        mSsid = "AndroidAP_7356";
        mBssid = "d6:48:2e:ea:80:02";
        mPassword = "g9gys47ivtw4gad";
        mSecurityMode = 40;
        mAccessPointType = 0;
        Log.d("ssid", mSsid);
        Log.d("mBssid", mBssid);
        //Log.d("mMACAddress",mMACAddress);
        Log.d("ipAddressString", ipAddressString);
        Log.d("mSecurityMode", String.valueOf(mSecurityMode));

        Log.d("mPassword", mPassword);
        Log.d("mAccessPointType", String.valueOf(mAccessPointType));
    }

    private final BroadcastReceiver mReceiver = new BroadcastReceiver() {
        @Override
        public void onReceive(Context context, Intent intent) {
            String action = intent.getAction();
            BluetoothDevice device = intent.getParcelableExtra(BluetoothDevice.EXTRA_DEVICE);
            Boolean mBluetoothState = false;
            if (BluetoothDevice.ACTION_FOUND.equals(action)) {
                //Device found
                Log.d(TAG, "ACTION_FOUND");
            } else if (BluetoothDevice.ACTION_ACL_CONNECTED.equals(action)) {
                //Device is now connected
                Log.d(TAG, "Device is now connected");
                mBluetoothState = true;
            } else if (BluetoothAdapter.ACTION_DISCOVERY_FINISHED.equals(action)) {
                //Done searching
            } else if (BluetoothDevice.ACTION_ACL_DISCONNECT_REQUESTED.equals(action)) {
                //Device is about to disconnect
            } else if (BluetoothDevice.ACTION_ACL_DISCONNECTED.equals(action)) {
                //Device has disconnected
                Log.d(TAG, "Device is disconnected");
                mBluetoothState = false;
            }
        }
    };

    @SuppressLint({"MissingPermission", "WrongConstant"})
    private void BluetoothConnectionChecker() {
        mBluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
        if (mBluetoothAdapter != null) {
            if (mBluetoothAdapter.isEnabled()) {
                if (mBluetoothAdapter.getProfileConnectionState(BluetoothProfile.GATT)
                        == BluetoothProfile.STATE_CONNECTED ||
                        mBluetoothAdapter.getProfileConnectionState(BluetoothProfile.GATT_SERVER) ==
                                BluetoothProfile.STATE_CONNECTED) {
                    Log.d(TAG, "bt connected");
                } else {
                    Log.d(TAG, "bt don`t connect");
                }
            } else {
                Log.d(TAG, "bt isEnabled ��false��");
            }
        } else {
            Log.d(TAG, "mBluetoothAdapter is null");
        }
    }

    @Override
    protected void onNewIntent(Intent intent) {
        super.onNewIntent(intent);
        Log.d(TAG, "onNewIntent");
        if (intent.getBooleanExtra("stop", false)) {
            mByeByeHandler.onByeByeRequest(0);
            System.exit(0);
        }

    }

    @Override
    protected void onResume() {
        super.onResume();
        Log.d(TAG, "onResume");
        // If we were projecting, return video focus
        Log.d("zzztest", "resume-111");
        SharedPreferences prefs = getSharedPreferences("app_prefs", MODE_PRIVATE);
        boolean returningFromHome = prefs.getBoolean("returningFromHome", false);
        Log.d("zzztest", "onResume: returningFromHome is: " + returningFromHome);
        returningFromHome = false;
        if (returningFromHome) {
            int currentWidth = mSurfaceView.getWidth();
            int currentHeight = mSurfaceView.getHeight();
            SharedPreferences originalSize = getSharedPreferences("app_prefs_size", MODE_PRIVATE);
            originalWidth = originalSize.getInt("initialWidth", 0);
            originalHeight = originalSize.getInt("initialHeight", 0);
            Log.d("zzztest", "onResume: originalWidth is: " + originalWidth);
            Log.d("zzztest", "onResume: originalHeight is: " + originalHeight);
            Log.d("zzztest", "onResume: mSurfaceView.getTop() is: " + mSurfaceView.getTop());
            Log.d("zzztest", "onResume: mSurfaceView.getLeft() is: " + mSurfaceView.getLeft());
            if (currentWidth != originalWidth || currentHeight != originalHeight) {
                Log.d("zzztest", "onResume: currentWidth is: " + currentWidth);
                Log.d("zzztest", "onResume: currentHeight is: " + currentHeight);
                ViewGroup.LayoutParams layoutParams = mSurfaceView.getLayoutParams();
                if (layoutParams instanceof LinearLayout.LayoutParams) {
                    LinearLayout.LayoutParams linearParams = (LinearLayout.LayoutParams) layoutParams;
                    linearParams.width = originalWidth;
                    linearParams.height = originalHeight + 250;
                    linearParams.setMargins(0, 0, 0, 0);
                    mSurfaceView.setLayoutParams(linearParams);
                }
                Log.d("zzztest", "onResume: mSurfaceView.getTop() is: " + mSurfaceView.getTop());
                Log.d("zzztest", "onResume: mSurfaceView.getLeft() is: " + mSurfaceView.getLeft());
            }
        }
        if (mProjectionRenderer != null && mProjectionRenderer.isStarted()) {
            Log.d("zzztest", "onResume: VIDEO_FOCUS_PROJECTED is: " + VideoSink.VIDEO_FOCUS_PROJECTED);
            mGal.mainDisplayVideoSink.setVideoFocus(
                    VideoSink.VIDEO_FOCUS_PROJECTED, -1 /* reason = not set */, true /* unsolicited */);
        }
//        SystemUiHider hider = new SystemUiHider(mLayout);
//        hider.setup();
    }
    private void hideSystemUi() {
        WindowCompat.setDecorFitsSystemWindows(getWindow(),false);

        WindowInsetsControllerCompat controller = new WindowInsetsControllerCompat(getWindow(), getWindow().getDecorView());
        controller.hide(WindowInsetsCompat.Type.systemBars());
        controller.setSystemBarsBehavior(WindowInsetsControllerCompat.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE);
    }

    @Override
    protected void onPause() {
        super.onPause();
        Log.d(TAG, "onPause");
        ret = 0;
        // If we are projecting, take away video focus
        if (mProjectionRenderer != null && mProjectionRenderer.isStarted()) {
            mGal.mainDisplayVideoSink.setVideoFocus(
                    VideoSink.VIDEO_FOCUS_NATIVE, -1 /* reason = not set */, true /* unsolicited */);
        }
        SharedPreferences prefs = getSharedPreferences("app_prefs", MODE_PRIVATE);
        SharedPreferences.Editor editor = prefs.edit();
        editor.putBoolean("returningFromHome", true);
        editor.apply();
    }

    private void startAccessoryMode(UsbAccessory accessory) {
        Log.d(TAG, "startAccessoryMode");
        Log.i(TAG, "Starting in accessory mode");
        if (accessory == null) {
            Log.e(TAG, "accessory is null");
        }
        UsbManager usbManager = (UsbManager) getSystemService(Context.USB_SERVICE);
        // Check whether we have permission to access the accessory.
        if (!usbManager.hasPermission(accessory)) {
            //TODO handling of case where permission is acquired later is currently missing.
            Log.i(TAG, "Prompting the user for access to the accessory.");
            Intent permissionIntent = new Intent(ACTION_USB_ACCESSORY_PERMISSION);
            permissionIntent.setPackage(getPackageName());
            PendingIntent pendingIntent = PendingIntent.getBroadcast(
                    this, 0, permissionIntent, PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE);
            usbManager.requestPermission(accessory, pendingIntent);
            return;
        }
        try {

            mTransportFactory.open(accessory, this);
            showConnections();
        } catch (IOException e) {
            Log.e(TAG, "Failed to start connection with " + accessory, e);
            System.exit(0);
        }
    }

    private void startHostMode(UsbDevice device) {
        Log.d(TAG, "startHostMode");
        // The whole operation ends up showing three pop-ups for permission.
        // First, for starting Activity for non-AOAP mode. Second, for Broadcast.
        // And third, for Activity in AOAP mode again.
        // Once user gives permission for all three, next connection will launch Activity only once
        // with handling AOAP from Broadcast. If we skip Broadcast, pop-up can be reduced into
        // two, but Activity should be launched twice, which makes things worse.
        // So keep Broadcast and make initial experience worse, but subsequent experience should
        // be better.
        Log.i(TAG, "Starting in host mode");
        if (!checkUsbHostPermission(device)) {
            System.exit(0);
            return;
        }
        if (UsbHostSetup.processDevice(device, this)) {
            Context context1 = this;
            showConnections();

            Handler handler = new Handler();
            handler.postDelayed(new Runnable() {
                @Override
                public void run() {
                    // �ӳ�ִ�еĴ���
                    try {
                        mTransportFactory.open(device, (TransportFactory.Callback) context1);
                    } catch (IOException e) {
                        throw new RuntimeException(e);
                    }
                }
            }, 0); // �ӳ� 0 ��

        } else {
            Log.i(TAG, "Not attached to accessory yet");
            System.exit(0);
        }
    }

    private boolean checkUsbHostPermission(UsbDevice device) {
        Log.d(TAG, "checkUsbHostPermission");
        UsbManager usbManager = (UsbManager) getSystemService(Context.USB_SERVICE);
        if (!usbManager.hasPermission(device)) {
            Log.i(TAG, "no permission for the device. asking user");
            Intent permissionIntent = new Intent(ACTION_USB_DEVICE_PERMISSION);
            PendingIntent pendingIntent = PendingIntent.getBroadcast(
                    this, 0, permissionIntent, PendingIntent.FLAG_ONE_SHOT | PendingIntent.FLAG_IMMUTABLE);
            usbManager.requestPermission(device, pendingIntent);
            return false;
        }
        return true;
    }

    public void startTcpServerMode() {
        Log.d(TAG, "startTcpServerMode");
        if (mTransportHandle != null) {
            try {
                mTransportHandle.interrupt();
            } catch (Exception e) {
                Log.w(TAG, "Failed to interrupt previous attempt to establish Transport", e);
            }
        }
        try {
            showConnections();
            mTransportHandle = mTransportFactory.open(null, this);
        } catch (IOException e) {
            Log.e(TAG, "Failed to start TCP server on port " + mSettings.getListenTcpPort(), e);
            System.exit(0);
        }
        Log.i(TAG, "Starting in TCP server mode on port " + mSettings.getListenTcpPort());
    }

    private void initializeSensorServices() {
        Log.d(TAG, "initializeSensorServices");
        if (mSettings.isGpsTabletEnabled()) {
            // Request location updates
            Log.d(TAG, "Start in GPS mode");
        } else if (mSettings.isGpsSimulationEnabled()) {
            // Enter simulation mode
            Log.d(TAG, "Start in simulation mode");
        }
        mSensorDispatcher = new SensorManager();
        mSensorDispatcher.init(
                new FakeSensors(this),
                new SimulatedLocationSensors(this, mDebugContext),
                new LocationSensors(this, mDebugContext),
                new TabletSensors(this),
                new SimpleObdSensor(this));
    }

    @Override
    public void onConfigurationChanged(@NonNull Configuration newConfig) {
        super.onConfigurationChanged(newConfig);
        Log.e("ConfigChange", "Configuration changed while I was away! " + newConfig);
    }

    private void initializeViews() {
        Log.d(TAG, "initializeViews");
        getWindow().addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON);

        setContentView(R.layout.activity_main);
        mLayout = (RelativeLayout) findViewById(R.id.frame);
        mConnectLayout = findViewById(R.id.connect_layout);
        mSurfaceView = (SurfaceView) findViewById(R.id.surface);
    }


    private void showConnections() {
        Log.d(TAG, "showConnections");
        BluetoothConnectionChecker();
        mConnectLayout.setVisibility(View.VISIBLE);
    }

    /**
     * Public method to override the transport with one that was manually created.
     *
     * @param transport
     */
    public void startConnectionEstablishing(final Transport transport) {
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                showConnections();
                onConnected(transport);
            }
        });
    }

    @Override
    protected void onDestroy() {
        Log.d(TAG, "onDestroy");
//        if (mTransportFactory != null) {
//            mTransportFactory.shutdown();
//            mTransportFactory = null;
//        }
//
//        if (mTestTools != null) {
//            mTestTools = null;
//        }
//
//        if (mSensorDispatcher != null) {
//            mSensorDispatcher.shutdown();
//            mSensorDispatcher = null;
//        }
//
//        if (mDebugContext != null) {
//            mDebugContext.shutdown();
//            mDebugContext = null;
//        }
//
//        if (mProjectionRenderer != null) {
//            mProjectionRenderer.release();
//        }
//
//        if (mBluetoothSocket != null) {
//            try {
//                mBluetoothSocket.close();
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        }
//
//        if (socket != null) {
//            try {
//                socket.close();
//            } catch (IOException e) {
//                throw new RuntimeException(e);
//            }
//        }
//        LocalBroadcastManager.getInstance(this).unregisterReceiver(mUsbDisconnectedReceiver);
//        mBluetoothListener.unregisterBluetoothStateListener(new BluetoothListener.BluetoothStateListener() {
//            @Override
//            public void onBluetoothStateChanged(boolean state) {
//
//            }
//        });
//
////        if (mBluetoothStartBroadcastReceiver != null) {
////            unregisterReceiver(mBluetoothStartBroadcastReceiver);
////        }
//        if (mReceiver != null) {
//            unregisterReceiver(mReceiver);
//        }
//
//        if (mapIpReceiver != null) {
//            unregisterReceiver(mapIpReceiver);
//        }
//
//        System.exit(0);
        super.onDestroy();
        Log.d(TAG, "onDestroy");
//        try {
//            if (codecConfigData != null && !hasWrittenHeader) {
//                //Log.d(TAG,"codecConfigData is not null");
//                // 1. ��д���������ݣ�SPS/PPS��
//                mFileOutputStream.write(codecConfigData);
//
//                // 2. д�뻺���е�����֡����
//                mFileOutputStream.write(mBufferStream.toByteArray());
//                mFileOutputStream.flush(); // ˢ�»�������ȷ������д�����
//                hasWrittenHeader = true;
//                mBufferStream.reset(); // ��ջ��棬׼����һ�λ���
//            }
//
//            // 3. �ر���
//            mFileOutputStream.close();
//            mBufferStream.close();
//
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
    }

    @Override
    public void onBackPressed() {
        Log.d(TAG, "onbackpressed");
        synchronized (mLock) {
            if (mGal != null) {
                mGal.mainDisplayInputSource.sendBackEvent();
            }
        }
    }

    /**
     * Starts the GAL receiver.
     */
    @SuppressLint("UnspecifiedRegisterReceiverFlag")
    private void startGAL(int width, int height) {
        Log.d(TAG, "startGAL");
        synchronized (mLock) {
            if (mGal != null) {
                return;
            }

            // We have a display, start talking to the phone now.
            Rect videoSize = DimensionUtils.makeRequestedVideoSize(width, height, mEmulatedScreen);
            mGal = new GalIntegration();
            AudioSink.AudioListener[] audioListeners = {
                    mAudioSinkMediaStreamPlayer,
                    mAudioSinkTtsStreamPlayer,
                    mAudioSinkSystemStreamPlayer};
            mNavigationFocusManager = new NavigationFocusManager(mGal);
            mAudioFocusManager =
                    new AudioFocusManager(mGal, mSettings.isRestrictedAudioFocusUsed());
            mAudioFocusManager.registerFocusChangeListener(mAudioSinkMediaStreamPlayer);
            mAudioFocusManager.registerFocusChangeListener(mAudioSinkTtsStreamPlayer);
            mAudioFocusManager.registerFocusChangeListener(mAudioSinkSystemStreamPlayer);
            mAudioSinkMediaStreamPlayer.registerAudioFocusManager(mAudioFocusManager);
            mAudioSinkTtsStreamPlayer.registerAudioFocusManager(mAudioFocusManager);
            mAudioSinkSystemStreamPlayer.registerAudioFocusManager(mAudioFocusManager);
            BluetoothEndpoint.BluetoothEventListener bluetoothEventListener =
                    new BluetoothEndpoint.BluetoothEventListener() {
                        @Override
                        public void onPairingRequest(String phoneAddress, int pairingMethod) {
                            Log.i(TAG, "onPairingRequest: phoneAddress=" + phoneAddress
                                    + " pairingMethod=" + pairingMethod);
                            new Handler(getMainLooper()).post(new Runnable() {
                                @Override
                                public void run() {
                                    Button bluetoothPairingResponseButton =
                                            (Button) findViewById(R.id.bluetooth_pairing_response_button);
                                    bluetoothPairingResponseButton.setEnabled(true);
                                    Button bluetoothAuthDataButton =
                                            (Button) findViewById(R.id.bluetooth_auth_data_button);
                                    bluetoothAuthDataButton.setEnabled(true);
                                }
                            });
                        }

                        @Override
                        public void onAuthenticationResult(int status) {
                            Log.i(TAG, "onAuthenticationResult: status=" + status);
                        }
                    };
            mTestTools = new TestTools(MainActivity.this, mDebugContext, mGal, mAudioFocusManager,
                    mNavigationFocusManager, mSensorDispatcher, mLayout);
            InstrumentClusterListener instrumentClusterListener =
                    new InstrumentClusterListener(mTestTools.getTestUiUpdateHandler());

            GalReceiver.CarInfo carInfo;
            String manafacturer = "Google";
            String year = "2014";
            @SuppressLint("HardwareIds")
            String vehicleId = android.provider.Settings.Secure.getString(getContentResolver(),
                    android.provider.Settings.Secure.ANDROID_ID);
            int driverPosition = GalReceiver.DRIVER_SIDE_LEFT;
            String huMake = "AHU";
            String huSwBuild = "";
            String huSwVersion = "";
            try {
                PackageInfo packageInfo = getPackageManager().getPackageInfo(getPackageName(), 0);
                huSwVersion = packageInfo.versionName;
            } catch (NameNotFoundException e) {
                // ignore
            }
            boolean canPlayNativeMediaDuringVr = false;

            String model;
            String huModel;
            if (mLocalMode) {
                model = "GalReceiver-Local";
                huModel = "Local";
            } else {
                model = "GalReceiver";
                huModel = "Projected";
            }
            int sessionConfiguration = 0;

            carInfo = new GalReceiver.CarInfo(manafacturer, model, year, vehicleId,
                    driverPosition, huMake, huModel, huSwBuild, huSwVersion,
                    canPlayNativeMediaDuringVr, sessionConfiguration, manafacturer);

            List<Integer> supportedSensors = mSensorDispatcher.getSupportedSensors();
            int[] sensors = new int[supportedSensors.size()];
            for (int i = 0; i < supportedSensors.size(); ++i) {
                sensors[i] = supportedSensors.get(i);
            }

            mGal.init(
                    mSettings.getCarBluetoothAddress(),
                    mSettings.getSupportedBluetoothPairingMethods(),
                    sensors,
                    mSurfaceView,
                    mProjectionListener,
                    mSettings.grantFocusOnStart(),
                    audioListeners,
                    mAudioSourceListener,
                    mAppMessageListener,
                    mAudioFocusListener,
                    mProjectedModeIconReceiver,
                    mByeByeHandler,
                    mUserSwitchListener,
                    bluetoothEventListener,
                    instrumentClusterListener,
                    mAudioFocusManager,
                    carInfo,
                    new RadioStub(),
                    new CarWifiStub(),
                    mHandler,
                    mLocalModeCallbacks.getRootCert(),
                    mLocalModeCallbacks.getClientCert(),
                    mLocalModeCallbacks.getPrivateKey(),
                    "00:11:22:aa:bb:cc" /** fake car wifi BSSID */
            );

            int fps = mSettings.isFpsLimited() ? 30 : 60;
            VideoConfiguration videoConfiguration =
                    mGal.mainDisplayVideoSink.nearestVideoConfiguration(
                            videoSize.width(),
                            videoSize.height(),
                            (int) mEmulatedScreen.getEmulatedDpi(),
                            (int) mEmulatedScreen.getPhysicalDpi(),
                            fps,
                            DECODER_ADDITIONAL_DEPTH);
            //mGal.mainDisplayVideoSink.addSupportedConfiguration(videoConfiguration);
            // add two more configs as the first one may not be supported.

            //С����200
            //������240
            videoConfiguration =
                    mGal.mainDisplayVideoSink.nearestVideoConfiguration(
                            mSurfaceView.getWidth(),
                            mSurfaceView.getHeight(),
                            240,
                            240,
                            fps,
                            DECODER_ADDITIONAL_DEPTH);
            //mGal.mainDisplayVideoSink.addSupportedConfiguration(videoConfiguration);
            videoConfiguration =
                    mGal.mainDisplayVideoSink.nearestVideoConfiguration(
                            1920,
                            960,
                            240,
                            240,
                            fps,
                            DECODER_ADDITIONAL_DEPTH);
            mGal.mainDisplayVideoSink.addSupportedConfiguration(videoConfiguration);
//      videoConfiguration =
//          mGal.mainDisplayVideoSink.nearestVideoConfiguration(
//              800,
//              480,
//              ((int) mEmulatedScreen.getEmulatedDpi()) * 800 / videoSize.width(),
//              (int) mEmulatedScreen.getPhysicalDpi(),
//              fps,
//              DECODER_ADDITIONAL_DEPTH);
            //mGal.mainDisplayVideoSink.addSupportedConfiguration(videoConfiguration);
            mGal.mainDisplayVideoSink.setCodecType(MediaCodecType.MEDIA_CODEC_VIDEO_H264_BP);

            mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_MEDIA]
                    .addSupportedConfiguration(SAMPLING_RATE_MEDIA,
                            AUDIO_BITS_MEDIA,
                            AUDIO_CHANNELS_MEDIA);
            mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_MEDIA]
                    .setAudioType(AudioStreamType.AUDIO_STREAM_MEDIA);
            mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_MEDIA]
                    .setCodecType(mSettings.isAAcEnabledForMedia()
                            ? MediaCodecType.MEDIA_CODEC_AUDIO_AAC_LC
                            : MediaCodecType.MEDIA_CODEC_AUDIO_PCM);

            mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_TTS]
                    .addSupportedConfiguration(SAMPLING_RATE_TTS,
                            AUDIO_BITS_TTS,
                            AUDIO_CHANNELS_TTS);
            mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_TTS]
                    .setAudioType(AudioStreamType.AUDIO_STREAM_GUIDANCE);
            mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_TTS]
                    .setCodecType(mSettings.isAAcEnabledForTts()
                            ? MediaCodecType.MEDIA_CODEC_AUDIO_AAC_LC
                            : MediaCodecType.MEDIA_CODEC_AUDIO_PCM);

            mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_SYSTEM]
                    .addSupportedConfiguration(SAMPLING_RATE_SYSTEM,
                            AUDIO_BITS_SYSTEM,
                            AUDIO_CHANNELS_SYSTEM);
            mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_SYSTEM]
                    .setAudioType(AudioStreamType.AUDIO_STREAM_SYSTEM_AUDIO);
            mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_SYSTEM]
                    .setCodecType(MediaCodecType.MEDIA_CODEC_AUDIO_PCM);

            mAudioSourceListener.setGalAudioSource(mGal.audioSource);

            int[] keys = {
                    // These are the same numerically so the can be interchanged.
                    KeyEvent.KEYCODE_DPAD_UP,
                    KeyEvent.KEYCODE_DPAD_DOWN,
                    KeyEvent.KEYCODE_DPAD_LEFT,
                    KeyEvent.KEYCODE_DPAD_RIGHT,
                    KeyEvent.KEYCODE_DPAD_CENTER,
                    KeyEvent.KEYCODE_SEARCH,
                    KeyEvent.KEYCODE_BACK,
                    KeyEvent.KEYCODE_MEDIA_PLAY,
                    KeyEvent.KEYCODE_MEDIA_PAUSE,
                    KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE,
                    KeyEvent.KEYCODE_MEDIA_PREVIOUS,
                    KeyEvent.KEYCODE_MEDIA_NEXT,
                    KeyEvent.KEYCODE_MUSIC,
                    KeyEvent.KEYCODE_CALL,
                    KeyEvent.KEYCODE_ENDCALL,
                    KeyCode.KEYCODE_NAVIGATION.getNumber(),
                    KeyCode.KEYCODE_TEL.getNumber(),
            };
            if (mSettings.isRotaryControllerEnabled()) {
                keys = Arrays.copyOf(keys, keys.length + 1);
                // This one isn't numerically the same so use the proto version.
                keys[keys.length - 1] = KeyCode.KEYCODE_ROTARY_CONTROLLER.getNumber();
            }
            mGal.mainDisplayInputSource.registerKeyCodes(keys);
            if (mSettings.isTouchScreenEnabled()) {
                mGal.mainDisplayInputSource.registerTouchScreen(
                        width, height, InputSource.TOUCHSCREEN_TYPE_CAPACITIVE);
            }
            View touchpad = findViewById(R.id.touchpad);
            boolean touchpadNavigation = mSettings.isTouchpadNavigationEnabled();
            mGal.mainDisplayInputSource.registerTouchPad(
                    touchpad.getWidth(), // width
                    touchpad.getHeight(), // height
                    touchpadNavigation, // uiNavigation
                    50, // physicalWidth
                    50, // physicalHeight
                    false, // uiAbsolute
                    false // tapAsSelect
            );
            mGal.mainDisplayInputSource.registerFeedbackEvents(
                    new int[]{
                            InputSource.FEEDBACK_EVENT_SELECT,
                            InputSource.FEEDBACK_EVENT_DRAG_SELECT,
                            InputSource.FEEDBACK_EVENT_FOCUS_CHANGE,
                    },
                    new InputSource.OnInputFeedbackListener() {
                        @SuppressLint("MissingPermission")
                        @Override
                        public void onInputFeedback(int event) {
                            long[] pattern = null;
                            if (event == InputSource.FEEDBACK_EVENT_SELECT) {
                                pattern = new long[]{0, 75};
                            } else if (event == InputSource.FEEDBACK_EVENT_FOCUS_CHANGE) {
                                // TODO figure out how to do a low-strength buzz
                            } else if (event == InputSource.FEEDBACK_EVENT_DRAG_SELECT) {
                                pattern = new long[]{0, 75};
                                // TODO figure out how to do a medium-strength buzz with a soft attack
                            }
                            if (pattern != null) {
                                Vibrator v = (Vibrator) getSystemService(Context.VIBRATOR_SERVICE);
                                v.vibrate(pattern, -1);
                            }
                        }
                    });

            mGal.audioSource.setConfig(mAudioSourceListener.getSamplingRate(),
                    mAudioSourceListener.getNumberOfBits(),
                    mAudioSourceListener.getNumberOfChannels());
            mAudioSinkMediaStreamPlayer.registerAudioSink(
                    mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_MEDIA]);
            mAudioSinkTtsStreamPlayer.registerAudioSink(
                    mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_TTS]);
            mAudioSinkSystemStreamPlayer.registerAudioSink(
                    mGal.audioSinks[GalIntegration.AUDIO_SINK_INDEX_SYSTEM]);
            mGal.start(mTransport);
            mSensorDispatcher.setGalIntegration(mGal);
            // If we're running in a test harness, permanently enable the keyboard.
            if (ActivityManager.isRunningInTestHarness()) {
                mTestTools.forceKeyboardLockoutOverride();
            }
        }
    }

    private final ProjectionRenderer.Listener mProjectionRendererListener =
            new ProjectionRenderer.Listener() {
                @Override
                public void onSurfaceCreated(int width, int height, boolean layoutChanged) {
                    Log.d(TAG, "onSurfaceCreated");
                    Log.d("zzzzzzsurface", "width:" + width + " " + "height:" + height);
                    if (originalWidth == 0 && originalHeight == 0) {
                        originalWidth = mSurfaceView.getWidth();
                        originalHeight = mSurfaceView.getHeight();
                        Log.d("zzzzzzsurface", "originalWidth:" + originalWidth + " " + "originalHeight:" + originalHeight);

                        SharedPreferences prefs = context.getSharedPreferences("app_prefs_size", Context.MODE_PRIVATE);
                        SharedPreferences.Editor editor = prefs.edit();
                        editor.putInt("initialWidth", originalWidth);
                        editor.putInt("initialHeight", originalHeight);
                        editor.apply();
                    }
                }

                @Override
                public void onSurfaceDestroyed(boolean layoutChanged) {
                    Log.d(TAG, "onSurfaceDestroyed");
                }

                @Override
                public void onFrameRendered(int sessionId) {
                    synchronized (mLock) {
                        if (mGal != null) {
                            mGal.mainDisplayVideoSink.ackFrames(sessionId, 1);
                        }
                    }
                }

//          @Override
//          public void onFrameSaved(String filePath) {
//              Log.d(TAG, "onFrameSaved: " + filePath);
//          }
            };


    private final VideoSink.ProjectionListener mProjectionListener =
            new VideoSink.ProjectionListener() {
                private AlertDialog mVideoFocusDialog;

                @Override
                public void onProjectionUpdate(VideoFrame frame) {
//            ByteBuffer frameData = frame.data;
//            byte[] buffer = new byte[frameData.remaining()];//create byte array
//            frameData.get(buffer);
//            String filename = (frameIndex++) + ".h264";
//            File file = new File(getFilesDir(), filename);
//            if (file.exists()) {
//                file.delete();
//                Log.d("zzzzfile","delete file");
//            }
//            Log.d("zzzzfile",  "filename: " + filename  + "getFilesDir: " + getFilesDir());

//                try {
//                    mBufferStream.write(buffer); //cash
//                    //Log.d("zzzzfile", "onProjectionUpdate2 Written Frame Data Length: " + buffer.length);
//                } catch (IOException e) {
//                    Log.e(TAG, "Error writing projection update data", e);
//                }
                    // д���ļ�
//            try (FileOutputStream fos = new FileOutputStream(file)) {
//                fos.write(buffer);
//                Log.d(TAG, "Saved frame to: " + file.getAbsolutePath());
//            } catch (IOException e) {
//                Log.e(TAG, "Error writing frame to file: " + filename, e);
//            }

                    mProjectionRenderer.decodeBuffer(frame);


                }

                @Override
                public void onSourceVideoConfigChanged(int width, int height, VideoConfiguration config) {
                    Log.d(TAG, "onSourceVideoConfigChanged");
                    mProjectionRenderer.prepareStart(width, height);
                    // As this can involve layout change, should run in UI thread.
                    runOnUiThread(
                            new Runnable() {
                                @Override
                                public void run() {
                                    mProjectionRenderer.start();
                                }
                            });
                }

                @Override
                public void onCodecConfig(byte[] data) {
                    Log.d(TAG, "onCodecConfig");
                    //codecConfigData = data;
                    mProjectionRenderer.handleCodecConfig(data);

                }

                @Override
                public void onCodecSetup(int mediaCodecType) {
                    // nothing to do
                }

                @Override
                public void onVideoFocusRequest(final int focus, final int reason) {
                    Log.d(TAG, "onVideoFocusRequest");
                    runOnUiThread(
                            new Runnable() {
                                @Override
                                public void run() {
                                    if (mSettings.showVideoFocusDialog()) {
                                        if (mVideoFocusDialog == null) {
                                            AlertDialog.Builder builder = new AlertDialog.Builder(MainActivity.this);
                                            builder
                                                    .setMessage("Phone has requested video focus")
                                                    .setTitle("Video Focus Request From Phone")
                                                    .setPositiveButton(
                                                            "Grant",
                                                            new DialogInterface.OnClickListener() {
                                                                @Override
                                                                public void onClick(DialogInterface dialog, int id) {
                                                                    mGal.mainDisplayVideoSink.setVideoFocus(focus, reason, false);
                                                                }
                                                            })
                                                    .setNegativeButton("Deny", null);
                                            mVideoFocusDialog = builder.create();
                                        }
                                        mVideoFocusDialog.show();
                                    } else {
                                        // grant video focus without asking user permission
                                        mGal.mainDisplayVideoSink.setVideoFocus(focus, reason, false);
                                    }
                                }
                            });
                }

                @Override
                public void onVideoFocusModeChange(final int focusMode, final int reason) {
                    Log.d(TAG, "onVideoFocusModeChange");
                    Log.d("zzztest", "focusMode: " + focusMode + "reason" + reason);
                    runOnUiThread(
                            new Runnable() {
                                @SuppressLint("MissingPermission")
                                @Override
                                public void run() {
                                    Log.d("zzztest", "onVideoFocusModeChange: ");
                                    if (focusMode == VideoSink.VIDEO_FOCUS_PROJECTED) {
                                        Log.d("zzztest", "111  VIDEO_FOCUS_PROJECTED");
                                        mTestTools.setNativeContentEnabled(false, "");

                                    } else {
                                        Log.d("zzztest", "222");
                                        String message = PHONE_SCREEN_OFF_MSG;
                                        mTestTools.setNativeContentEnabled(true, focusMode != Protos.VideoFocusReason.PHONE_SCREEN_OFF.getNumber()
                                                ? MainActivity.this.getString(R.string.native_content) : MainActivity.PHONE_SCREEN_OFF_MSG);
//                    if (reason != VideoFocusReason.PHONE_SCREEN_OFF.getNumber()) {
//                        Log.d("zzztest","333 PHONE_SCREEN_OFF");
//                      message = getString(R.string.native_content);
                                        Intent intent = new Intent(Intent.ACTION_MAIN);
                                        intent.addCategory(Intent.CATEGORY_HOME);
                                        startActivity(intent);
//                    }
//                      Log.d("zzztest","444");
//                    //mTestTools.setNativeContentEnabled(true, message);
                                    }
                                }
                            });
                }
            };

    @SuppressWarnings("unused")
    private final AudioPlayer mAudioSinkMediaStreamPlayer =
            new AudioPlayer(AudioManager.STREAM_MUSIC,
                    SAMPLING_RATE_MEDIA,
                    (AUDIO_CHANNELS_MEDIA == 2)
                            ? AudioFormat.CHANNEL_OUT_STEREO : AudioFormat.CHANNEL_OUT_MONO,
                    (AUDIO_BITS_MEDIA == 16)
                            ? AudioFormat.ENCODING_PCM_16BIT : AudioFormat.ENCODING_PCM_8BIT,
                    DBG_LATENCY ? mAudioLatencyChecker : null);

    @SuppressWarnings("unused")
    private final AudioPlayer mAudioSinkTtsStreamPlayer =
            new AudioPlayer(AudioManager.STREAM_NOTIFICATION,
                    SAMPLING_RATE_TTS,
                    (AUDIO_CHANNELS_TTS == 2)
                            ? AudioFormat.CHANNEL_OUT_STEREO : AudioFormat.CHANNEL_OUT_MONO,
                    (AUDIO_BITS_TTS == 16)
                            ? AudioFormat.ENCODING_PCM_16BIT : AudioFormat.ENCODING_PCM_8BIT,
                    null);

    @SuppressWarnings("unused")
    private final AudioPlayer mAudioSinkSystemStreamPlayer =
            new AudioPlayer(AudioManager.STREAM_SYSTEM,
                    SAMPLING_RATE_SYSTEM,
                    (AUDIO_CHANNELS_SYSTEM == 2)
                            ? AudioFormat.CHANNEL_OUT_STEREO : AudioFormat.CHANNEL_OUT_MONO,
                    (AUDIO_BITS_SYSTEM == 16)
                            ? AudioFormat.ENCODING_PCM_16BIT : AudioFormat.ENCODING_PCM_8BIT,
                    null);

    private final MicrophoneRecorder mAudioSourceListener = new MicrophoneRecorder();

    @Override
    public void onConnected(final Transport transport) {
        Log.d(TAG, "onConnected");
        runOnUiThread(new Runnable() {
            @SuppressLint("ClickableViewAccessibility")
            @Override
            public void run() {
                Log.d(TAG, "Transport connected");
                // First start the projection renderer so that it is completely up and running
                // before we start talking to the phone.
                mTransportHandle = null;
                mTransport = transport;
                mConnectLayout.setVisibility(View.GONE);

                TextView fpsView = (TextView) findViewById(R.id.fps);

                mSurfaceView.setOnTouchListener(mSurfaceTouchListener);

                int emulatedWidth = mSettings.getEmulatedWidth();
                int emulatedHeight = mSettings.getEmulatedHeight();
                float emulatedDpi = mSettings.getEmulatedDpi();
                DisplayMetrics metrics = getResources().getDisplayMetrics();
                float realDpi = (metrics.xdpi + metrics.ydpi) / 2;
                boolean scaleToScreen = mSettings.isScaledToScreen();
                if (emulatedWidth == 0 || emulatedHeight == 0 || emulatedDpi == 0) {
                    emulatedWidth = emulatedHeight = 0;
                    emulatedDpi = realDpi;
                }
                mEmulatedScreen = new DimensionUtils.ScreenSettings(emulatedWidth, emulatedHeight,
                        emulatedDpi, realDpi, scaleToScreen);
                mProjectionRenderer = new ProjectionRenderer(mSurfaceView, fpsView,
                        mProjectionRendererListener, mEmulatedScreen,
                        DBG_LATENCY ? mVideoLatencyChecker : null,
                        DBG_LATENCY ? mAudioLatencyChecker : null);
                mSurfaceView.setVisibility(View.VISIBLE);

                final Rect renderFrame = mProjectionRenderer.getRenderFrame();
                startGAL(renderFrame.right, renderFrame.bottom);
            }
        });
    }

    @Override
    public void onDisconnected() {
        Log.d(TAG, "onDisconnected");
        runOnUiThread(new Runnable() {
            @Override
            public void run() {
                Log.d(TAG, "Transport disconnected");

                if (mProjectionRenderer != null) {
                    Log.d(TAG, "shutdown renderer");
                    mProjectionRenderer.stop();
                }
                if (mAudioSourceListener != null) {
                    mAudioSourceListener.stop();
                }
                if (mAudioSinkMediaStreamPlayer != null) {
                    Log.d(TAG, "shutdown media audio");
                    mAudioSinkMediaStreamPlayer.tearDown();
                }
                if (mAudioSinkTtsStreamPlayer != null) {
                    Log.d(TAG, "shutdown tts audio");
                    mAudioSinkTtsStreamPlayer.tearDown();
                }
                if (mAudioSinkSystemStreamPlayer != null) {
                    Log.d(TAG, "shutdown system audio");
                    mAudioSinkSystemStreamPlayer.tearDown();
                }

                synchronized (mLock) {
                    if (mGal != null) {
                        mGal.destroy();
                        mGal = null;
                        mTransport = null;
                    }
                }
                System.exit(0);
                // Don't finish if listening for TCP connection, keep listening
                if (mLaunchMode == LAUNCHER_MODE) {
                    if (mSurfaceView != null) {
                        mSurfaceView.setVisibility(View.INVISIBLE);
                    }
                    showConnections();
                } else {
                    if (mSensorDispatcher != null) {
                        mSensorDispatcher.shutdown();
                        mSensorDispatcher = null;
                    }

                    if (!MainActivity.this.isFinishing()) {
                        if (mIntentControllerReceiver != null) {
                            unregisterReceiver(mIntentControllerReceiver);
                            mIntentControllerReceiver = null;
                        }
                        System.exit(0);
                    }
                }
            }
        });
    }

    private boolean handleKeyEvent(int keycode, KeyEvent event) {
        boolean handled = false;
        Log.d("zzzzzz", "keycode: " + keycode);
        if (mGal == null || mGal.mainDisplayInputSource == null) {
            // We got a touch event as we are shutting down.
            return handled;
        }
        switch (keycode) {
            case KeyEvent.KEYCODE_DPAD_UP:
            case KeyEvent.KEYCODE_DPAD_DOWN:
            case KeyEvent.KEYCODE_DPAD_LEFT:
            case KeyEvent.KEYCODE_DPAD_RIGHT:
            case KeyEvent.KEYCODE_DPAD_CENTER:
            case KeyEvent.KEYCODE_SEARCH:
            case KeyEvent.KEYCODE_BACK:
            case KeyEvent.KEYCODE_MEDIA_PLAY:
            case KeyEvent.KEYCODE_MEDIA_PAUSE:
            case KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE:
            case KeyEvent.KEYCODE_MEDIA_PREVIOUS:
            case KeyEvent.KEYCODE_MEDIA_NEXT:
                mGal.mainDisplayInputSource.sendKeyEvent(event);
                handled = true;
                break;
            case KeyEvent.KEYCODE_ENTER:
                KeyEvent e = new KeyEvent(event.getAction(), KeyEvent.KEYCODE_DPAD_CENTER);
                mGal.mainDisplayInputSource.sendKeyEvent(e);
                handled = true;
                break;
            case KeyEvent.KEYCODE_1:
            case KeyEvent.KEYCODE_BUTTON_L1:
                if (event.getAction() == KeyEvent.ACTION_UP) {
                    mGal.mainDisplayInputSource.sendRelativeEvent(
                            KeyCode.KEYCODE_ROTARY_CONTROLLER.getNumber(), -1);
                    handled = true;
                }
                break;
            case KeyEvent.KEYCODE_2:
            case KeyEvent.KEYCODE_BUTTON_R1:
                if (event.getAction() == KeyEvent.ACTION_UP) {
                    mGal.mainDisplayInputSource.sendRelativeEvent(
                            KeyCode.KEYCODE_ROTARY_CONTROLLER.getNumber(), 1);
                    handled = true;
                }
                break;
            case KeyEvent.KEYCODE_VOLUME_DOWN:
            case KeyEvent.KEYCODE_VOLUME_UP:
                if (mSettings.volumeToggleDebug()) {
                    if (event.getAction() == KeyEvent.ACTION_UP
                            && (mSettings.isDebugMode() || mTestTools != null)) {
                        mTestTools.toggleDebugToolbarVisibility();
                    }
                    handled = true;
                }
                break;
            default:
                Log.i(TAG, "Got keycode: " + keycode);
                break;
        }
        return handled;
    }

    @Override
    public boolean onKeyDown(int keycode, KeyEvent event) {
        return handleKeyEvent(keycode, event);
    }

    @Override
    public boolean onKeyUp(int keycode, KeyEvent event) {
        return handleKeyEvent(keycode, event);
    }

    @Override
    public boolean dispatchGenericMotionEvent(MotionEvent ev) {
        Log.d(TAG, "dispatchGenericMotionEvent");
        if (mGal != null && mGal.mainDisplayInputSource != null) {
            if (ev.getAction() == MotionEvent.ACTION_MOVE) {
                float lValue = ev.getAxisValue(MotionEvent.AXIS_LTRIGGER);
                float rValue = ev.getAxisValue(MotionEvent.AXIS_RTRIGGER);
                // Max of 5 ticks every 200ms
                // Negative means rotate left.
                final int ticks = (int) ((rValue - lValue) * 5);
                mHandler.removeCallbacksAndMessages(null);
                if (ticks != 0) {
                    mGal.mainDisplayInputSource.sendRelativeEvent(
                            KeyCode.KEYCODE_ROTARY_CONTROLLER.getNumber(), ticks);
                    mHandler.postDelayed(
                            new Runnable() {
                                @Override
                                public void run() {
                                    if (mGal != null && mGal.mainDisplayInputSource != null) {
                                        mGal.mainDisplayInputSource.sendRelativeEvent(
                                                KeyCode.KEYCODE_ROTARY_CONTROLLER.getNumber(), ticks);
                                        mHandler.postDelayed(this, 200);
                                    }
                                }
                            },
                            200);
                }
            }
        }
        return super.dispatchGenericMotionEvent(ev);
    }

    private final OnTouchListener mSurfaceTouchListener =
            new OnTouchListener() {
                private boolean mInGesture;

                @Override
                public boolean onTouch(View view, MotionEvent event) {
                    // Squelch all messages if we're currently in gesture.
                    if (mInGesture) {
                        if (event.getAction() == MotionEvent.ACTION_UP) {
                            mInGesture = false;
                        }
                        return true;
                    }
                    if ((mSettings.isDebugMode() || mTestTools != null)
                            && event.getAction() == MotionEvent.ACTION_DOWN
                            && event.getX() >= (view.getWidth() - 20)
                            && !ActivityManager.isUserAMonkey()) {
                        mInGesture = true;
                        mTestTools.toggleDebugToolbarVisibility();
                        return true;
                    }
                    if (mSettings.isTouchScreenEnabled()) {
                        synchronized (mLock) {
                            if (mGal != null) {
                                // Transform event to video's coordinate system.
                                MotionEvent ev = mProjectionRenderer.transformMotion(event);
                                if (mVideoLatencyChecker != null) {
                                    mVideoLatencyChecker.onInputEvent(event);
                                }
                                if (mAudioLatencyChecker != null) {
                                    mAudioLatencyChecker.onInputEvent(event);
                                }
                                Trace.beginSection("sendTouchEvent ev action : " + ev.getAction());
                                mGal.mainDisplayInputSource.sendTouchEvent(ev);
                                Trace.endSection();
                                ev.recycle();
                            }
                        }
                    }
                    return true;
                }
            };

    private final GalReceiver.AppMessageListener mAppMessageListener =
            new GalReceiver.AppMessageListener() {
                @Override
                public void onNavigationFocusRequest(final int focusType) {
                    Log.d(TAG, "onNavigationFocusRequest");
                    Log.i(TAG, "onNavigationFocusRequest: " + focusType);
                    runOnUiThread(new Runnable() {
                        @Override
                        public void run() {
                            mNavigationFocusManager.handleNavigationFocusRequest(focusType);
                        }
                    });
                }

                @Override
                public void onVoiceSessionNotification(int state) {
                    Log.i(TAG, "onVoiceSessionNotification, state:" + state);
                }

                @Override
                public void onBatteryStatusNotification(int batteryLevel, int timeRemainingS,
                                                        boolean batteryCritical) {
                    Log.d(TAG, "Battery status: " + batteryLevel + " percent remaining/"
                            + timeRemainingS + " seconds remaining");
                }

                @Override
                public void onUnrecoverableError(MessageStatus status) {
                    Log.e(TAG, "Received unrecoverable error " + status + ". Shutting down.");
                    mGal.stop(StopReason.CONNECTION_ERROR);
                }

                @Override
                public void onDisconnected(StopReason stopReason) {
                    Log.d(TAG, "onDisconnected");
                    Log.i(TAG, "Shutting down GAL/transport: " + stopReason);
                    MainActivity.this.onDisconnected();
                    if (mLaunchMode != LAUNCHER_MODE) {
                        LocalBroadcastManager manager =
                                LocalBroadcastManager.getInstance(MainActivity.this);
                        Intent intent = new Intent(GalIntegration.USB_DISCONNECT_INTENT);
                        manager.sendBroadcast(intent);
                    }
                }

                @Override
                public void onPoorWirelessNetworkConnectivity() {
                    Log.w(TAG, "Wireless connectivity is poor");
                }
            };

    private final GalReceiver.AudioFocusListener mAudioFocusListener =
            new GalReceiver.AudioFocusListener() {
                @Override
                public void onAudioFocusRequest(int request) {
                    mAudioFocusManager.handleAudioFocusRequest(request);
                }
            };

    private final GalReceiver.ProjectedModeIconReceiver mProjectedModeIconReceiver =
            new GalReceiver.ProjectedModeIconReceiver() {
                @Override
                public void setProjectedModeIcon(byte[] iconArray, String label) {
                    Bitmap smallIconMap = null;
                    smallIconMap = BitmapFactory.decodeByteArray(iconArray, 0, iconArray.length);
                    BitmapDrawable bmd = new BitmapDrawable(getApplicationContext().getResources(),
                            smallIconMap);
                    bmd.setAntiAlias(true);
                    InsetDrawable insetDrawable = new InsetDrawable(bmd, 15, 0, -15, 0);
                    mTestTools.setApplicationIcon(insetDrawable, label);
                }
            };

    private final GalReceiver.ByeByeHandler mByeByeHandler = new GalReceiver.ByeByeHandler() {
        @Override
        public void onByeByeRequest(int reason) {
            Log.i(TAG, "received ByeByeRequest with reason " + reason);
            Log.d(TAG, "onByeByeRequest");
            if (mGal != null && mGal.galReceiver != null) {
                mGal.galReceiver.sendByeByeResponse();
            }
            if (mGal != null) {
                mGal.stop(StopReason.MD_BYEBYE);
            }
        }

        @Override
        public void onByeByeResponse() {
            Log.i(TAG, "received ByeByeResponse");
            if (mGal != null) {
                mGal.stop(StopReason.HU_BYEBYE);
            }
        }
    };

    private final GalReceiver.UserSwitchListener mUserSwitchListener =
            new GalReceiver.UserSwitchListener() {
                @Override
                public void onCarConnectedDevicesRequest() {
                    Log.d(TAG, "onCarConnectedDevicesRequest");
                    Log.i(TAG, "onCarConnectedDevicesRequest");
                    @SuppressLint("MissingPermission")
                    Set<BluetoothDevice> pairedDevices = BluetoothAdapter.getDefaultAdapter()
                            .getBondedDevices();
                    List<ConnectedDevice> connectedDevices = new ArrayList<>(pairedDevices.size());
                    int deviceID = 1;
                    for (BluetoothDevice device : pairedDevices) {
                        @SuppressLint("MissingPermission")
                        ConnectedDevice cDevice = new ConnectedDevice(device.getName(), deviceID++);
                        connectedDevices.add(cDevice);
                    }

                    mGal.galReceiver.sendCarConnectedDevices(connectedDevices, false, true);
                }

                @SuppressLint("MissingPermission")
                @Override
                public void onUserSwitchRequest(ConnectedDevice selectedDevice) {
                    Log.d(TAG, "onUserSwitchRequest");
                    Log.i(TAG, "onUserSwitchRequest");
                    // User switch not supported, just send updated device list to phone
                    BluetoothAdapter bluetoothAdapter = BluetoothAdapter.getDefaultAdapter();
                    if (bluetoothAdapter == null) {
                        return;
                    }
                    Set<BluetoothDevice> pairedDevices = bluetoothAdapter.getBondedDevices();
                    List<ConnectedDevice> connectedDevices = new ArrayList<>(pairedDevices.size());
                    int deviceID = 1;
                    for (BluetoothDevice device : pairedDevices) {
                        if (device.getName().equals(selectedDevice.deviceName)) {
                            continue;
                        }
                        ConnectedDevice cDevice = new ConnectedDevice(device.getName(), deviceID++);
                        connectedDevices.add(cDevice);
                    }
                    mGal.galReceiver.sendCarConnectedDevices(connectedDevices, true, true);
                }
            };
}

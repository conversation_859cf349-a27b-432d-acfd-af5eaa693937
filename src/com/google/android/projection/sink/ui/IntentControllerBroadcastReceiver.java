// Copyright 2015 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.ui;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;

import com.google.android.projection.sink.GalIntegration;
import com.google.android.projection.sink.debug.DebugContext;
import com.google.android.projection.sink.settings.Settings;

/**
 * Allows access to parts of the debug panel via intents.
 * This does not bother keeping the UI in sync.
 */
public class IntentControllerBroadcastReceiver extends BroadcastReceiver {
    private static final String TAG = "AHU.ICR";
    public final static String NIGHT_MODE_ACTION = "com.google.android.projection.sink.NIGHT_MODE";
    public final static String GPS_ACTION = "com.google.android.projection.sink.GPS";
    public final static String LOCKOUT_ACTION = "com.google.android.projection.sink.LOCKOUT";
    public final static String ARG_KEY = "arg";

    private final DebugContext mDebugContext;
    private final GalIntegration mGal;

    public IntentControllerBroadcastReceiver(DebugContext context, GalIntegration gal) {
        mDebugContext = context;
        mGal = gal;
    }
    @Override
    public void onReceive(Context context, Intent intent) {
        Bundle extras = intent.getExtras();
        Settings settings = new Settings(context);

        if (extras == null) {
            Log.w(TAG, "no extras. discarding.");
            return;
        }

        switch (intent.getAction()) {
            case GPS_ACTION:
                if (settings.isGpsSimulationEnabled() && extras.containsKey(ARG_KEY)) {
                    switch (extras.getString(ARG_KEY)) {
                        case "reset":
                            mDebugContext.getSimulatedLocationProvider().resetSimulation();
                            break;
                        case "increaseSpeed":
                            mDebugContext.getSimulatedLocationProvider().increaseSimulationSpeed();
                            break;
                        case "decreaseSpeed":
                            mDebugContext.getSimulatedLocationProvider().decreaseSimulationSpeed();
                            break;
                    }
                }
                break;
            case NIGHT_MODE_ACTION:
                if (extras.containsKey(ARG_KEY)) {
                    switch (extras.getString(ARG_KEY)) {
                        case "night":
                            mGal.galVerificationVendorExtension.overrideNightMode(true);
                            break;
                        case "day":
                            mGal.galVerificationVendorExtension.overrideNightMode(false);
                            break;
                        default:
                            mGal.galVerificationVendorExtension.overrideNightMode(null);
                    }
                }
                break;
            case LOCKOUT_ACTION:
                if (extras.containsKey(ARG_KEY)) {
                    switch (extras.getString(ARG_KEY)) {
                        case "override":
                            mDebugContext.setKeyboardLockoutMode(
                                    DebugContext.KEYBOARD_LOCKOUT_OVERRIDE);
                            break;
                        case "auto":
                            mDebugContext.setKeyboardLockoutMode(
                                    DebugContext.KEYBOARD_LOCKOUT_AUTO);
                            break;
                        case "force":
                            mDebugContext.setKeyboardLockoutMode(
                                    DebugContext.KEYBOARD_LOCKOUT_FORCE);
                            break;
                    }
                }
                break;
        }
    }

    public static IntentFilter getIntentFilter() {
        IntentFilter filter = new IntentFilter();
        filter.addAction(GPS_ACTION);
        filter.addAction(NIGHT_MODE_ACTION);
        filter.addAction(LOCKOUT_ACTION);
        return filter;
    }
}

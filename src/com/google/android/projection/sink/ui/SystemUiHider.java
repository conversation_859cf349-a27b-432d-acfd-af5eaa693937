// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.ui;

import android.app.ActivityManager;
import android.os.Handler;
import android.view.View;

public class SystemUiHider {
    private static final int HIDE_DELAY_MILLIS = 2000;

    private Handler mHandler;
    private final View mView;

    public SystemUiHider(View view) {
        mView = view;
    }

    public void setup() {
        hideSystemUi();

        mHandler = new Handler();
        mView.setOnSystemUiVisibilityChangeListener(new View.OnSystemUiVisibilityChangeListener() {
            @Override
            public void onSystemUiVisibilityChange(int visibility) {
                if ((visibility & View.SYSTEM_UI_FLAG_HIDE_NAVIGATION) == 0) {
                    delay();
                }
            }
        });
    }

    private void hideSystemUi() {
        mView.setSystemUiVisibility(View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_FULLSCREEN
                | View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                | View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                | View.SYSTEM_UI_FLAG_IMMERSIVE);
    }

    private final Runnable mHideRunnable = new Runnable() {
        @Override
        public void run() {
            hideSystemUi();
        }
    };

    public void delay() {
        mHandler.removeCallbacks(mHideRunnable);
        mHandler.postDelayed(mHideRunnable, ActivityManager.isUserAMonkey() ?
                0 : HIDE_DELAY_MILLIS);
    }
}


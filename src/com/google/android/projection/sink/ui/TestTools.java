package com.google.android.projection.sink.ui;

import android.content.Context;
import android.graphics.Bitmap;
import android.graphics.BitmapFactory;
import android.graphics.drawable.Drawable;
import android.os.Handler;
import android.os.Message;
import android.util.Log;
import android.view.KeyEvent;
import android.view.MotionEvent;
import android.view.View;
import android.view.View.OnClickListener;
import android.widget.Button;
import android.widget.CompoundButton;
import android.widget.CompoundButton.OnCheckedChangeListener;
import android.widget.EditText;
import android.widget.ImageView;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.ToggleButton;

import com.google.android.projection.proto.Protos.BluetoothPairingMethod;
import com.google.android.projection.proto.Protos.KeyCode;
import com.google.android.projection.protocol.AndroidHeadUnitLogging;
import com.google.android.projection.protocol.GalReceiver;
import com.google.android.projection.protocol.SensorSource;
import com.google.android.projection.protocol.VideoSink;
import com.google.android.projection.sink.GalIntegration;
import com.google.android.projection.sink.InstrumentClusterListener;
import com.google.android.projection.sink.R;
import com.google.android.projection.sink.audio.AudioFocusManager;
import com.google.android.projection.sink.audio.NativeGuidancePlayer;
import com.google.android.projection.sink.audio.NativeMusicPlayer;
import com.google.android.projection.sink.debug.DebugContext;
import com.google.android.projection.sink.debug.SimulatedLocationProvider;
import com.google.android.projection.sink.navigation.NavigationFocusManager;
import com.google.android.projection.sink.sensors.SensorManager;
import com.google.android.projection.sink.settings.Settings;

/**
 * A pile of different debug controls.
 */
public class TestTools {
    private static final String TAG = AndroidHeadUnitLogging.TAG_MAIN_ACTIVITY;
    private static final int KEYBOARD_LOCKOUT_DISABLE_TIMEOUT = 5 * 60 * 1000;

    private final Context mContext;
    private final DebugContext mDebugContext;
    private final GalIntegration mGal;
    private final Settings mSettings;
    private final AudioFocusManager mAudioFocusManager;
    private final NavigationFocusManager mNavigationFocusManager;
    private final NativeMusicPlayer mNativeMusicPlayer;
    private final NativeGuidancePlayer mNativeGuidancePlayer;
    private final SensorManager mSensorDispatcher;

    private View mSimulatedLocationControls;
    private TextView mCurrentSpeedView;
    private final LinearLayout mDebugLayout;
    private final ToggleButton mVfToggle;
    private final ToggleButton mNavFocusToggle;
    private final Button mNativeMusicPlay;
    private final Button mNativeMusicStop;
    private final LinearLayout mNativeLayout;
    private final TextView mNativeBackgroundText;

    private final View mRootView;

    private final NativePanelUpdateHandler mNativePanelUpdateHandler =
            new NativePanelUpdateHandler();

    public TestTools(Context context, DebugContext debugContext, GalIntegration gal,
                     AudioFocusManager audioFocusManager, NavigationFocusManager navigationFocusManager,
                     SensorManager sensorDispatcher, final View rootView) {
        mContext = context;
        mDebugContext = debugContext;
        mGal = gal;
        mSettings = new Settings(mContext);
        mAudioFocusManager = audioFocusManager;
        mNavigationFocusManager = navigationFocusManager;
        mSensorDispatcher = sensorDispatcher;
        mRootView = rootView;

        mNativeLayout = (LinearLayout) mRootView.findViewById(R.id.native_layout);
        mNativeBackgroundText = (TextView) mRootView.findViewById(R.id.native_text);

        mNativeMusicPlayer = NativeMusicPlayer.createMusicPlayer(context,
                mAudioFocusManager,
                R.raw.john_harrison_with_the_wichita_state_university_chamber_players_05_summer_mvt_2_adagio);
        // sound used is not voice but alarm sound. But it is for testing handling of
        // guidance.
        mNativeGuidancePlayer = NativeGuidancePlayer.createGuidancePlayer(
                context,
                mAudioFocusManager,
                R.raw.alarm_classic);

        mDebugLayout = (LinearLayout) mRootView.findViewById(R.id.tools);
        mDebugLayout.setVisibility(View.INVISIBLE);

        mVfToggle = (ToggleButton) mRootView.findViewById(R.id.video_focus);
        mVfToggle.setChecked(true);
        mVfToggle.setOnCheckedChangeListener(
                new OnCheckedChangeListener() {
                    @Override
                    public void onCheckedChanged(CompoundButton button, boolean isChecked) {
                        mGal.mainDisplayVideoSink.setVideoFocus(
                                isChecked ? VideoSink.VIDEO_FOCUS_PROJECTED : VideoSink.VIDEO_FOCUS_NATIVE,
                                -1,
                                true);
                    }
                });
        ToggleButton audioFocusToggle = (ToggleButton) mRootView.findViewById(R.id.audio_focus);
        audioFocusToggle.setChecked(true);
        audioFocusToggle.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton button, boolean isChecked) {
                if (mAudioFocusManager != null) {
                    if (isChecked) {
                        mAudioFocusManager.enablePhoneFocus();
                    } else {
                        mAudioFocusManager.disablePhoneFocus();
                    }
                }
            }
        });
        Button beepAudio = (Button) mRootView.findViewById(R.id.native_transient_audio);
        beepAudio.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mNativeGuidancePlayer != null) {
                    mNativeGuidancePlayer.play();
                }
            }
        });
        mNativeMusicPlay = (Button) mRootView.findViewById(R.id.native_music_play);
        mNativeMusicPlay.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mNativeMusicPlayer != null) {
                    Log.i(TAG, "play native music");
                    mNativeMusicPlayer.play();
                }
            }
        });
        mNativeMusicStop = (Button) mRootView.findViewById(R.id.native_music_stop);
        mNativeMusicStop.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                if (mNativeMusicPlayer != null) {
                    Log.i(TAG, "stop native music");
                    mNativeMusicPlayer.stop();
                }
            }
        });
        mNavFocusToggle = (ToggleButton) mRootView.findViewById(R.id.nav_focus);
        mNavFocusToggle.setChecked(false);
        mNavFocusToggle.setOnCheckedChangeListener(new OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(CompoundButton button, boolean isChecked) {
                mNavigationFocusManager.enforceNavigationFocus(!isChecked);
            }
        });
        if (!mSettings.isRotaryControllerEnabled()) {
            mRootView.findViewById(R.id.sim_dpad).setVisibility(View.GONE);
        }
        Button dpadUp = (Button) mRootView.findViewById(R.id.dpad_up);
        dpadUp.setOnTouchListener(mDpadTouchListener);
        Button dpadDown = (Button) mRootView.findViewById(R.id.dpad_down);
        dpadDown.setOnTouchListener(mDpadTouchListener);
        Button dpadLeft = (Button) mRootView.findViewById(R.id.dpad_left);
        dpadLeft.setOnTouchListener(mDpadTouchListener);
        Button dpadRight = (Button) mRootView.findViewById(R.id.dpad_right);
        dpadRight.setOnTouchListener(mDpadTouchListener);
        Button dpadCenter = (Button) mRootView.findViewById(R.id.dpad_center);
        dpadCenter.setOnTouchListener(mDpadTouchListener);
        Button cw = (Button) mRootView.findViewById(R.id.rotary_cw);
        cw.setOnTouchListener(mDpadTouchListener);
        Button ccw = (Button) mRootView.findViewById(R.id.rotary_ccw);
        ccw.setOnTouchListener(mDpadTouchListener);
        Button cwf = (Button) mRootView.findViewById(R.id.rotary_cw_fling);
        cwf.setOnTouchListener(mDpadTouchListener);
        Button ccwf = (Button) mRootView.findViewById(R.id.rotary_ccw_fling);
        ccwf.setOnTouchListener(mDpadTouchListener);
        mRootView
                .findViewById(R.id.touchpad)
                .setOnTouchListener(
                        new View.OnTouchListener() {
                            @Override
                            public boolean onTouch(View view, MotionEvent event) {
                                if (mGal != null) {
                                    mGal.mainDisplayInputSource.sendTouchPadEvent(event);
                                }
                                return true;
                            }
                        });

        Button call = (Button) mRootView.findViewById(R.id.call);
        call.setOnTouchListener(new ButtonTouchListener(KeyEvent.KEYCODE_CALL));
        Button endCall = (Button) mRootView.findViewById(R.id.end_call);
        endCall.setOnTouchListener(new ButtonTouchListener(KeyEvent.KEYCODE_ENDCALL));

        Button mediaPrev = (Button) mRootView.findViewById(R.id.media_prev);
        mediaPrev.setOnTouchListener(new ButtonTouchListener(KeyEvent.KEYCODE_MEDIA_PREVIOUS));
        Button mediaNext = (Button) mRootView.findViewById(R.id.media_next);
        mediaNext.setOnTouchListener(new ButtonTouchListener(KeyEvent.KEYCODE_MEDIA_NEXT));
        Button mediaPlay = (Button) mRootView.findViewById(R.id.media_play);
        mediaPlay.setOnTouchListener(new ButtonTouchListener(KeyEvent.KEYCODE_MEDIA_PLAY));
        Button mediaPause = (Button) mRootView.findViewById(R.id.media_pause);
        mediaPause.setOnTouchListener(new ButtonTouchListener(KeyEvent.KEYCODE_MEDIA_PAUSE));
        Button mediaPlayPause = (Button) mRootView.findViewById(R.id.media_play_pause);
        mediaPlayPause.setOnTouchListener(
                new ButtonTouchListener(KeyEvent.KEYCODE_MEDIA_PLAY_PAUSE));

        Button bvraButton = (Button) mRootView.findViewById(R.id.bvra_button);
        bvraButton.setOnTouchListener(new ButtonTouchListener(KeyEvent.KEYCODE_SEARCH));
        Button mediaButton = (Button) mRootView.findViewById(R.id.media_button);
        mediaButton.setOnTouchListener(new ButtonTouchListener(KeyEvent.KEYCODE_MUSIC));
        Button telButton = (Button) mRootView.findViewById(R.id.tel_button);
        telButton.setOnTouchListener(new ButtonTouchListener(KeyCode.KEYCODE_TEL.getNumber()));
        Button navButton = (Button) mRootView.findViewById(R.id.nav_button);
        navButton.setOnTouchListener(
                new ButtonTouchListener(KeyCode.KEYCODE_NAVIGATION.getNumber()));
        Button backButton = (Button) mRootView.findViewById(R.id.back_button);
        backButton.setOnTouchListener(new ButtonTouchListener(KeyEvent.KEYCODE_BACK));

        Button byebyeButton = (Button) mRootView.findViewById(R.id.byebye_button);
        byebyeButton.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View v) {
                mGal.galReceiver.sendByeByeRequest(GalReceiver.BYEBYE_REASON_USER_SELECTION);
            }
        });
        Button disconnectGVVEButton = (Button) mRootView.findViewById(R.id.disconnect_gvve_button);
        disconnectGVVEButton.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                if (mGal.galVerificationVendorExtension != null) {
                    mGal.galVerificationVendorExtension.closeChannel();
                }
            }
        });

        for (final int id : KEYBOARD_LOCKOUT_BUTTON_IDS) {
            ((ToggleButton) mRootView.findViewById(id)).setOnClickListener(
                    new OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            onKeyboardLockoutButtonClick(view);
                        }
                    });
        }

        Button bluetoothPairingResponseButton =
                (Button) mRootView.findViewById(R.id.bluetooth_pairing_response_button);
        bluetoothPairingResponseButton.setEnabled(false);
        bluetoothPairingResponseButton.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                ToggleButton bluetoothAlreadyPairedToggle =
                        (ToggleButton) mDebugLayout.findViewById(
                                R.id.bluetooth_already_paired_toggle);
                mGal.bluetooth.onReadyForPairing(
                        bluetoothAlreadyPairedToggle.isChecked());
            }
        });
        Button bluetoothAuthDataButton =
                (Button) mRootView.findViewById(R.id.bluetooth_auth_data_button);
        bluetoothAuthDataButton.setEnabled(false);
        bluetoothAuthDataButton.setOnClickListener(new OnClickListener() {
            @Override
            public void onClick(View view) {
                EditText bluetoothAuthDataText =
                        (EditText) mDebugLayout.findViewById(R.id.bluetooth_auth_data_text);
                mGal.bluetooth.sendAuthData(bluetoothAuthDataText.getText().toString(),
                        BluetoothPairingMethod.BLUETOOTH_PAIRING_NUMERIC_COMPARISON_VALUE);
            }
        });

        if (mSettings.isGpsSimulationEnabled()) {
            initializeSimulatorSpeedControls();
        }

        mNavigationFocusManager.registerListener(
                new NavigationFocusManager.NavigationFocusListener() {
                    @Override
                    public void onNavigationFocusChanged(boolean useNativeNavigation) {
                        mNavFocusToggle.setChecked(!useNativeNavigation);
                    }
                });

        final int[] nightModeButtons = {
                R.id.night_mode_auto, R.id.night_mode_day, R.id.night_mode_night};
        for (final int id : nightModeButtons) {
            ((ToggleButton) mRootView.findViewById(id)).setOnClickListener(
                    new OnClickListener() {
                        @Override
                        public void onClick(View view) {
                            for (int otherId : nightModeButtons) {
                                ((ToggleButton) mRootView.findViewById(otherId)).setChecked(
                                        otherId == view.getId());
                            }
                            if (view.getId() == R.id.night_mode_auto) {
                                mGal.galVerificationVendorExtension.overrideNightMode(null);
                            } else if (view.getId() == R.id.night_mode_day) {
                                mGal.galVerificationVendorExtension.overrideNightMode(false);
                            } else if (view.getId() == R.id.night_mode_night) {
                                mGal.galVerificationVendorExtension.overrideNightMode(true);
                            }
                        }
                    });
        }
    }

    static final int[] KEYBOARD_LOCKOUT_BUTTON_IDS = {
            R.id.keyboard_lockout_auto,
            R.id.keyboard_lockout_force,
            R.id.keyboard_lockout_override
    };

    private void onKeyboardLockoutButtonClick(View view) {
        // Cancel any pending timer.
        mNativePanelUpdateHandler.removeCallbacks(mKeyboardLockoutRevert);

        // Apply the correct mode for the button.
        if (view.getId() == R.id.keyboard_lockout_auto) {
            mDebugContext.setKeyboardLockoutMode(DebugContext.KEYBOARD_LOCKOUT_AUTO);
        } else if (view.getId() == R.id.keyboard_lockout_force) {
            mDebugContext.setKeyboardLockoutMode(DebugContext.KEYBOARD_LOCKOUT_FORCE);
        } else if (view.getId() == R.id.keyboard_lockout_override) {
            // Enable the override and set a timer to disable it.
            mDebugContext.setKeyboardLockoutMode(
                    DebugContext.KEYBOARD_LOCKOUT_OVERRIDE);
            mNativePanelUpdateHandler.postDelayed(
                    mKeyboardLockoutRevert, KEYBOARD_LOCKOUT_DISABLE_TIMEOUT);
        }
        updateKeyboardLockoutUi();
        mSensorDispatcher.reportAvailable(SensorSource.SENSOR_TYPE_DRIVING_STATUS);
    }

    private void updateKeyboardLockoutUi() {
        // Make sure the clicked button is toggled to on, and everything else to off.
        ((ToggleButton) mRootView.findViewById(R.id.keyboard_lockout_auto)).setChecked(
                mDebugContext.getKeyboardLockoutMode() == DebugContext.KEYBOARD_LOCKOUT_AUTO);
        ((ToggleButton) mRootView.findViewById(R.id.keyboard_lockout_force)).setChecked(
                mDebugContext.getKeyboardLockoutMode() == DebugContext.KEYBOARD_LOCKOUT_FORCE);
        ((ToggleButton) mRootView.findViewById(R.id.keyboard_lockout_override)).setChecked(
                mDebugContext.getKeyboardLockoutMode() == DebugContext.KEYBOARD_LOCKOUT_OVERRIDE);
    }

    private final Runnable mKeyboardLockoutRevert = new Runnable() {
        @Override
        public void run() {
            // Toggle the button back to auto (by simulating a click on the auto button).
            onKeyboardLockoutButtonClick(mRootView.findViewById(R.id.keyboard_lockout_auto));
        }
    };

    public void forceKeyboardLockoutOverride() {
        mNativePanelUpdateHandler.removeCallbacks(mKeyboardLockoutRevert);
        mDebugContext.setKeyboardLockoutMode(DebugContext.KEYBOARD_LOCKOUT_OVERRIDE);
        updateKeyboardLockoutUi();
    }

    public void toggleDebugToolbarVisibility() {
        if (mDebugLayout.getVisibility() == View.VISIBLE) {
            mDebugLayout.setVisibility(View.INVISIBLE);
        } else {
            mDebugLayout.setVisibility(View.VISIBLE);
        }
    }

    public void setNativeContentEnabled(boolean enabled, String message) {
        mVfToggle.setChecked(!enabled);
        mNativeBackgroundText.setText(message);
        mNativeLayout.setVisibility(enabled ? View.VISIBLE : View.GONE);
    }

    public void setApplicationIcon(Drawable icon, String label) {
        mNativePanelUpdateHandler.setApplicationIcon(icon, label);
    }

    public Handler getTestUiUpdateHandler() {
        return mNativePanelUpdateHandler;
    }

    /**
     * Initializes simulator speed controls.
     */
    private void initializeSimulatorSpeedControls() {
        mSimulatedLocationControls = mDebugLayout.findViewById(R.id.simulated_location_controls);
        // Stop pass-thru of touch events from speed controls.
        mSimulatedLocationControls.setClickable(true);

        // Current speed indicator
        mCurrentSpeedView = (TextView) mSimulatedLocationControls.findViewById(
                R.id.simulated_location_current_speed);
        updateSpeedControls(0);

        // Increase/Decrease speed buttons
        mSimulatedLocationControls.findViewById(R.id.simulated_location_faster).setOnClickListener(
                new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mDebugContext.getSimulatedLocationProvider().increaseSimulationSpeed();
                    }
                });
        mSimulatedLocationControls.findViewById(R.id.simulated_location_slower).setOnClickListener(
                new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mDebugContext.getSimulatedLocationProvider().decreaseSimulationSpeed();
                    }
                });
        mSimulatedLocationControls.findViewById(R.id.simulated_location_reset).setOnClickListener(
                new View.OnClickListener() {
                    @Override
                    public void onClick(View v) {
                        mDebugContext.getSimulatedLocationProvider().resetSimulation();
                    }
                });
        mSimulatedLocationControls.setVisibility(View.VISIBLE);

        mDebugContext.getSimulatedLocationProvider().registerUiListener(
                new SimulatedLocationProvider.UiListener() {
                    @Override
                    public void onSpeedChanged(int speedMultiplier) {
                        updateSpeedControls(speedMultiplier);
                    }
                });
    }

    /**
     * Updates the display of the speed controls.
     */
    private void updateSpeedControls(int routeSpeedFactor) {
        if (mCurrentSpeedView == null) {
            return;
        }
        String speedText = routeSpeedFactor == 0
                ? mContext.getString(R.string.simulated_location_stopped)
                : String.format(mContext.getString(R.string.simulated_location_format),
                routeSpeedFactor);
        mCurrentSpeedView.setText(speedText);
    }

    private final View.OnTouchListener mDpadTouchListener =
            new View.OnTouchListener() {
                @Override
                public boolean onTouch(View view, MotionEvent event) {
                    int action = event.getActionMasked();
                    if (action != MotionEvent.ACTION_DOWN && action != MotionEvent.ACTION_UP) {
                        return false;
                    }

                    boolean handled = true;
                    int keycode = 0;
                    boolean isKey = true;
                    int delta = 0;
                    int id = view.getId();
                    if (id == R.id.dpad_up) {
                        keycode = KeyEvent.KEYCODE_DPAD_UP;
                    } else if (id == R.id.dpad_down) {
                        keycode = KeyEvent.KEYCODE_DPAD_DOWN;
                    } else if (id == R.id.dpad_left) {
                        keycode = KeyEvent.KEYCODE_DPAD_LEFT;
                    } else if (id == R.id.dpad_right) {
                        keycode = KeyEvent.KEYCODE_DPAD_RIGHT;
                    } else if (id == R.id.dpad_center) {
                        keycode = KeyEvent.KEYCODE_DPAD_CENTER;
                    } else if (id == R.id.rotary_ccw) {
                        keycode = KeyCode.KEYCODE_ROTARY_CONTROLLER.getNumber();
                        delta = -1;
                        isKey = false;
                    } else if (id == R.id.rotary_cw) {
                        keycode = KeyCode.KEYCODE_ROTARY_CONTROLLER.getNumber();
                        delta = 1;
                        isKey = false;
                    } else if (id == R.id.rotary_ccw_fling) {
                        keycode = KeyCode.KEYCODE_ROTARY_CONTROLLER.getNumber();
                        delta = -5;
                        isKey = false;
                    } else if (id == R.id.rotary_cw_fling) {
                        keycode = KeyCode.KEYCODE_ROTARY_CONTROLLER.getNumber();
                        delta = 5;
                        isKey = false;
                    } else {
                        handled = false;
                    }
                    if (handled && mGal != null) {
                        if (isKey) {
                            KeyEvent keyEvent = new KeyEvent(action, keycode);
                            mGal.mainDisplayInputSource.sendKeyEvent(keyEvent);
                        } else if (action == MotionEvent.ACTION_DOWN) {
                            // Don't send 2 events for relative events.
                            mGal.mainDisplayInputSource.sendRelativeEvent(keycode, delta);
                        }
                    }
                    return handled;
                }
            };

    private class ButtonTouchListener implements View.OnTouchListener {
        private final int mKeyCode;

        public ButtonTouchListener(int keyCode) {
            mKeyCode = keyCode;
        }

        @Override
        public boolean onTouch(View v, MotionEvent event) {
            int action = event.getActionMasked();
            if (action != MotionEvent.ACTION_DOWN && action != MotionEvent.ACTION_UP) {
                return false;
            }
            KeyEvent keyEvent = new KeyEvent(action, mKeyCode);
            mGal.mainDisplayInputSource.sendKeyEvent(keyEvent);
            return true;
        }
    }

    /**
     * Handles updates to the native debug panel.
     */
    private class NativePanelUpdateHandler extends Handler {
        private Drawable mIcon;
        private String mLabel;

        public void setApplicationIcon(Drawable icon, String label) {
            mIcon = icon;
            mLabel = label;
            sendEmptyMessage(InstrumentClusterListener.UPDATE_VTOGGLE_TEXT);
        }

        @Override
        public void handleMessage(Message msg) {
            switch (msg.what) {
                case InstrumentClusterListener.UPDATE_VTOGGLE_TEXT:
                    ToggleButton vfToggle =
                            (ToggleButton) mDebugLayout.findViewById(R.id.video_focus);
                    vfToggle.setButtonDrawable(mIcon);
                    vfToggle.setTextOn(vfToggle.getTextOn() + " (" + mLabel + ")");
                    vfToggle.setTextOff(vfToggle.getTextOff() + " (" + mLabel + ")");
                    if (vfToggle.isChecked()) {
                        vfToggle.setText(vfToggle.getTextOn());
                    } else {
                        vfToggle.setText(vfToggle.getTextOff());
                    }
                    break;
                case InstrumentClusterListener.UPDATE_IC_NAVIGATION:
                    String navigationString = msg.getData().getString(
                            InstrumentClusterListener.NAVIGATION_STRING);
                    byte[] bitmapBytes = msg.getData().getByteArray(
                            InstrumentClusterListener.NAVIGATION_BITMAP);
                    if (navigationString != null) {
                        TextView navigation =
                                (TextView) mDebugLayout.findViewById(R.id.navigation_text);
                        navigation.setText(navigationString);
                    }
                    if (bitmapBytes != null) {
                        Bitmap bitmap =
                                BitmapFactory.decodeByteArray(bitmapBytes, 0, bitmapBytes.length);
                        ImageView navigationImage =
                                (ImageView) mDebugLayout.findViewById(R.id.navigation_image);
                        navigationImage.setImageBitmap(bitmap);
                    }
                    break;
                case InstrumentClusterListener.UPDATE_IC_MEDIA_STATUS:
                    String mediaStatusString = msg.getData().getString(
                            InstrumentClusterListener.MEDIA_STATUS_STRING);
                    if (mediaStatusString != null) {
                        TextView mediaStatus =
                                (TextView) mDebugLayout.findViewById(R.id.media_status_text);
                        mediaStatus.setText(mediaStatusString);
                    }
                    byte[] albumArt = msg.getData().getByteArray(
                            InstrumentClusterListener.MEDIA_ALBUM_ART);
                    if (albumArt != null) {
                        Bitmap bitmap =
                                BitmapFactory.decodeByteArray(albumArt, 0, albumArt.length);
                        ImageView mediaAlbumArt =
                                (ImageView) mDebugLayout.findViewById(R.id.media_album_art);
                        mediaAlbumArt.setImageBitmap(bitmap);
                    }
                    break;
                case InstrumentClusterListener.UPDATE_IC_MEDIA_BROWSER:
                    String rootNodeString = msg.getData().getString(
                            InstrumentClusterListener.MEDIA_BROWSER_ROOT_STRING);
                    if (rootNodeString != null) {
                        TextView mediaRootNode =
                                (TextView) mDebugLayout.findViewById(R.id.media_browser_root_text);
                        mediaRootNode.setText(rootNodeString);
                    }
                    String sourceNodeString = msg.getData().getString(
                            InstrumentClusterListener.MEDIA_BROWSER_SOURCE_STRING);
                    if (sourceNodeString != null) {
                        TextView mediaSourceNode =
                                (TextView) mDebugLayout.findViewById(
                                        R.id.media_browser_source_text);
                        mediaSourceNode.setText(sourceNodeString);
                    }
                    String listNodeString = msg.getData().getString(
                            InstrumentClusterListener.MEDIA_BROWSER_LIST_STRING);
                    if (listNodeString != null) {
                        TextView mediaListNode =
                                (TextView) mDebugLayout.findViewById(R.id.media_browser_list_text);
                        mediaListNode.setText(listNodeString);
                    }
                    String songNodeString = msg.getData().getString(
                            InstrumentClusterListener.MEDIA_BROWSER_SONG_STRING);
                    if (songNodeString != null) {
                        TextView mediaSongNode =
                                (TextView) mDebugLayout.findViewById(R.id.media_browser_song_text);
                        mediaSongNode.setText(songNodeString);
                    }
                    break;
                case InstrumentClusterListener.UPDATE_IC_PHONE_STATUS:
                    String phoneStatusString =
                            msg.getData().getString(InstrumentClusterListener.PHONE_STATUS_STRING);
                    if (phoneStatusString != null) {
                        TextView phoneStatus =
                                (TextView) mDebugLayout.findViewById(R.id.phone_status_text);
                        phoneStatus.setText(phoneStatusString);
                    }
                    byte[] photo = msg.getData().getByteArray(
                            InstrumentClusterListener.PHONE_STATUS_PHOTO);
                    ImageView callPhoto =
                            (ImageView) mDebugLayout.findViewById(R.id.phone_status_photo);
                    if (photo != null) {
                        Bitmap bitmap =
                                BitmapFactory.decodeByteArray(photo, 0, photo.length);
                        callPhoto.setVisibility(View.VISIBLE);
                        callPhoto.setImageBitmap(bitmap);
                    } else {
                        callPhoto.setVisibility(View.GONE);
                    }
            }
        }
    }
}

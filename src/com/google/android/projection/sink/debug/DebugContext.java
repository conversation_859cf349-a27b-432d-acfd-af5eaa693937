package com.google.android.projection.sink.debug;

import android.content.Context;

/**
 * A class that gives control over all mock/fake/simulated aspects of the app behavior.
 */
public class DebugContext {
    private final SimulatedLocationProvider mSimulatedLocationProvider;
    private int mKeyboardLockoutMode;

    public DebugContext(Context context) {
        mSimulatedLocationProvider = new SimulatedLocationProvider(context);
    }

    public void shutdown() {
        mSimulatedLocationProvider.shutdown();
    }

    public SimulatedLocationProvider getSimulatedLocationProvider() {
        return mSimulatedLocationProvider;
    }

    // Use location data.
    public final static int KEYBOARD_LOCKOUT_AUTO = 0;
    // Always locked out.
    public final static int KEYBOARD_LOCKOUT_FORCE = 1;
    // Temporarily enabled.
    public final static int KEYBOARD_LOCKOUT_OVERRIDE = 2;

    public void setKeyboardLockoutMode(int mode) {
        mKeyboardLockoutMode = mode;
    }

    public int getKeyboardLockoutMode() {
        return mKeyboardLockoutMode;
    }
}

// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.debug.simulation;

import android.location.Location;

import java.util.List;

/**
 * Simulates a sequence of locations along a route.
 */
public final class RouteSimulator {
    public static final String SINK_PROVIDER = "Sink";

    private final List<SimulationRoutePoint> mPoints;
    private final boolean mLoop;
    // The value at index i is the time from point i to point i + 1.
    private final double[] mPointTimes;
    private final double mTotalTime;

    // The number of seconds elapsed since the start of the route.
    private double mTimeElapsed;
    // The index of the earliest point at which we have not yet arrived.
    private int mNextPointIndex;
    // The remaining time to get the the nextPointIndexth point.
    private double mTimeToNextPoint;

    private boolean mFinished;

    /**
     * @param points Must have at least two elements. Not null.
     * @param loop True to loop back to the start after reaching the end of the
     *            route. False to stop.
     */
    public RouteSimulator(List<SimulationRoutePoint> points, boolean loop) {
        this.mPoints = points;
        this.mLoop = loop;

        mPointTimes = new double[points.size()];
        for (int i = 0; i < points.size() - 1; ++i) {
            SimulationRoutePoint p0 = points.get(i);
            SimulationRoutePoint p1 = points.get(i + 1);
            mPointTimes[i] = getDistance(p0.lat, p0.lng, p1.lat, p1.lng) / p0.speed;
        }
        mPointTimes[points.size() - 1] = 0;

        double totalTime = 0;
        for (double time : mPointTimes) {
            totalTime += time;
        }
        this.mTotalTime = totalTime;
    }

    /**
     * Moves the simulation forward by seconds seconds.
     *
     * @param seconds Greater than zero.
     */
    public void update(double seconds) {
        mTimeElapsed += seconds;
        mTimeToNextPoint -= seconds;

        while (mTimeToNextPoint < 0) {
            mTimeToNextPoint += mPointTimes[mNextPointIndex];
            ++mNextPointIndex;

            if (mNextPointIndex == mPoints.size()) {
                if (mLoop) {
                    mNextPointIndex = 0;
                    mTimeElapsed -= mTotalTime;
                } else {
                    mTimeToNextPoint = 0;
                    --mNextPointIndex;
                    mTimeElapsed = mTotalTime;
                    mFinished = true;
                }
            }
        }
    }

    public Location getLocation() {
        Location location = new Location(SINK_PROVIDER);
        if (mNextPointIndex == 0) {
            SimulationRoutePoint p0 = mPoints.get(0);
            SimulationRoutePoint p1 = mPoints.get(1);
            location.setLatitude(p0.lat);
            location.setLongitude(p0.lng);
            location.setBearing((float) getHeading(p0.lat, p0.lng, p1.lat, p1.lng));
            location.setSpeed((float) p0.speed);
        } else {
            int prevPointIndex = (mNextPointIndex == 0 ? mPoints.size() : mNextPointIndex) - 1;
            SimulationRoutePoint p0 = mPoints.get(prevPointIndex);
            SimulationRoutePoint p1 = mPoints.get(mNextPointIndex);
            double t = (mPointTimes[prevPointIndex] - mTimeToNextPoint) / mPointTimes[prevPointIndex];

            location.setLatitude(p0.lat * (1.0 - t) + p1.lat * t);
            location.setLongitude(p0.lng * (1.0 - t) + p1.lng * t);
            location.setBearing((float) getHeading(p0.lat, p0.lng, p1.lat, p1.lng));
            location.setSpeed(mFinished ? 0 : (float) p0.speed);
        }
        location.setTime(System.currentTimeMillis());
        location.setAccuracy(5);

        return location;
    }

    public double getProgress() {
        return mTimeElapsed / mTotalTime;
    }

    public void reset() {
        mNextPointIndex = 0;
        mTimeElapsed = 0;
    }

    private static final int METRES_PER_LATITUDE_DEGREE = 111120;

    /**
     * Returns the distance to the specified location in meters.
     */
    private static double getDistance(double lat0, double lng0, double lat1, double lng1) {
        double latDistance = Math.abs(lat1 - lat0) * METRES_PER_LATITUDE_DEGREE;
        double k = Math.cos(Math.toRadians(lat0));
        double metrePerLongitudeHere = k * METRES_PER_LATITUDE_DEGREE;
        double lngDistance = Math.abs(lng1 - lng0) * metrePerLongitudeHere;
        return Math.sqrt((latDistance * latDistance) + (lngDistance * lngDistance));
    }

    /**
     * Calculates the heading for even-log for the specified dest.
     */
    private static double getHeading(double lat0, double lng0, double lat1, double lng1) {
        // TODO(karlhendrikse): Avoid using approximated calculation
        double x = Math.toRadians(lng1 - lng0);
        double y = Math.toRadians(lat1 - lat0);

        double heading = Math.toDegrees(Math.atan2(x, y));

        return (heading + 360) % 360;
    }
}

// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.debug.simulation;

/**
 * A point in a route polyline along which to simulate. Immutable.
 */
public final class SimulationRoutePoint {
    /** Latitude in degrees. */
    public final double lat;
    /** Longitude in degrees. */
    public final double lng;
    /** The speed in meters per second from here to the next point. */
    public final double speed;

    /**
     * @param lat Latitude in degrees.
     * @param lng Longitude in degrees.
     * @param speed Speed in meters per second. Greater than zero.
     */
    public SimulationRoutePoint(double lat, double lng, double speed) {
        this.lat = lat;
        this.lng = lng;
        this.speed = speed;
    }
}

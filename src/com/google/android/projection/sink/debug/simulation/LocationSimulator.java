// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.debug.simulation;

import android.location.Location;
import android.os.Handler;
import android.util.Log;

import com.google.android.projection.protocol.AndroidHeadUnitLogging;

import java.util.List;

/**
 * Simulates locations from a route or a fixed position. All interaction with
 * instances of this class happens on the same thread.
 */
public final class LocationSimulator {
    private static final String TAG = AndroidHeadUnitLogging.TAG_LOCATION;
    private static final boolean DBG = false;

    /**
     * Gets told when there is a new location.
     */
    public interface Listener {
        /**
         * @param location Null if there is no location.
         * @param routeProgress Progress along the from in the range [0, 1], or
         *            Double.NaN if we're not simulating along a route.
         */
        void onSimulationUpdate(Location location, double routeProgress);
    }

    private final Listener mListener;

    private int mUpdatePeriodMillis;

    private Location mFixedLocation;

    private RouteSimulator mRouteSimulator;
    private int mRouteSpeedFactor = 1;
    private boolean mRouteResumed;

    private final Handler mHandler = new Handler();

    public LocationSimulator(Listener listener) {
        this.mListener = listener;
    }

    public void startUpdates(int updatePeriodMillis) {
        this.mUpdatePeriodMillis = updatePeriodMillis;
        stopUpdates();
        if (mRouteSimulator != null) {
            mUpdateRunnable.run();
        }
    }

    public void stopUpdates() {
        mHandler.removeCallbacks(mUpdateRunnable);
    }

    public void setFixedLocation(Location location) {
        mFixedLocation = location;
    }

    public void setRoute(List<SimulationRoutePoint> points) {
        if (DBG) {
            Log.d(TAG, "setRoute()");
        }
        if (mRouteSimulator != null) {
            mFixedLocation = getLocation();
        }

        if (points != null) {
            if (DBG) {
                Log.d(TAG, "RouteSimulator(" + points.size() + " points)");
            }
            mRouteSimulator = new RouteSimulator(points, false);
        } else {
            mRouteSimulator = null;
        }
    }

    public void setRouteSpeedFactor(int routeSpeedFactor) {
        this.mRouteSpeedFactor = routeSpeedFactor;
    }

    public int getRouteSpeedFactor() {
        return mRouteSpeedFactor;
    }

    public void resetRoute() {
        mRouteSimulator.reset();
    }

    public void setRouteResumed(boolean routeResumed) {
        this.mRouteResumed = routeResumed;
    }

    private final Runnable mUpdateRunnable = new Runnable() {
        @Override
        public void run() {
            // Do this first, in case the listener decides to stop the updates.
            mHandler.postDelayed(this, mUpdatePeriodMillis);

            if (mRouteSimulator != null) {
                mListener.onSimulationUpdate(getLocation(), getProgress());

                if (mRouteResumed) {
                    mRouteSimulator.update(mRouteSpeedFactor * mUpdatePeriodMillis / 1000.0);
                }
            }
        }
    };

    private Location getLocation() {
        if (mRouteSimulator != null) {
            Location location = mRouteSimulator.getLocation();
            location.setSpeed(mRouteResumed ? location.getSpeed() * mRouteSpeedFactor : 0);
            return location;
        }
        return mFixedLocation;
    }

    private double getProgress() {
        if (mRouteSimulator != null) {
            return mRouteSimulator.getProgress();
        }

        return Double.NaN;
    }
}

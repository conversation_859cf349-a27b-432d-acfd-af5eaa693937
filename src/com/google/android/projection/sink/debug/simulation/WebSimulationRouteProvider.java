// Copyright 2014 Google Inc. All Rights Reserved.

package com.google.android.projection.sink.debug.simulation;

import android.util.Log;
import com.google.android.projection.protocol.AndroidHeadUnitLogging;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.UnsupportedEncodingException;
import java.net.MalformedURLException;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Locale;
import org.json.JSONArray;
import org.json.JSONException;
import org.json.JSONObject;

/**
 * Calculates routes given a start and end address using the directions web API.
 */
public final class WebSimulationRouteProvider {
    private WebSimulationRouteProvider() {
    }

    private static final String TAG = AndroidHeadUnitLogging.TAG_LOCATION;

    /**
     * @param start Not null.
     * @param dest Not null.
     * @param travelMode Not null.
     */
    public static List<SimulationRoutePoint> createPoints(String start, String dest,
            String travelMode) {
        JSONObject jsonRoute = getJsonRoute(start, dest, travelMode);
        if (jsonRoute == null) {
            return null;
        }

        return createDrivingTrack(jsonRoute);
    }

    private static JSONObject getJsonRoute(String start, String dest, String travelMode) {
        URL url = getDirectionsUrl(start, dest, travelMode);
        if (url == null) {
            return null;
        }

        try {
            URLConnection urlConnection = url.openConnection();
            urlConnection.setRequestProperty("User-Agent",
                    "Google Map Navigation Beta Demo Launcher");
            BufferedReader reader =
                    new BufferedReader(new InputStreamReader(urlConnection.getInputStream()));

      StringBuilder jsonString = new StringBuilder();
            String line;
            while ((line = reader.readLine()) != null) {
                jsonString.append(line);
            }

            return new JSONObject(jsonString.toString()).getJSONArray("routes").getJSONObject(0);
        } catch (IOException exception) {
            Log.e(TAG, "Exception", exception);
            return null;
        } catch (JSONException exception) {
            Log.e(TAG, "Exception", exception);
            return null;
        }
    }

    /**
     * Generates the URL for the specified parameters for Google direction API.
     */
    private static URL getDirectionsUrl(String start, String dest, String travelMode) {
        try {
            return new URL("http://maps.googleapis.com/maps/api/directions/json"
                    + "?origin=" + URLEncoder.encode(start, "UTF-8")
                    + "&destination=" + URLEncoder.encode(dest, "UTF-8")
                    + "&mode=" + travelMode.toLowerCase(Locale.getDefault())
                    + "&sensor=false");
        } catch (MalformedURLException e) {
            Log.e(TAG, "Exception", e);
        } catch (UnsupportedEncodingException e) {
            Log.e(TAG, "Exception", e);
        }
        return null;
    }

    private static final class LatLngPoint {
        public final double lat;
        public final double lng;

        public LatLngPoint(double lat, double lng) {
            this.lat = lat;
            this.lng = lng;
        }
    }

    /**
     * Generates the list of driving locations per second for the specified
     * route.
     *
     * @param route the JSON notation for the route.
     */
    private static List<SimulationRoutePoint> createDrivingTrack(JSONObject route) {
        ArrayList<SimulationRoutePoint> points = new ArrayList<SimulationRoutePoint>();
        try {
            JSONArray steps = route.getJSONArray("legs").getJSONObject(0).getJSONArray("steps");
            for (int stepIndex = 0; stepIndex < steps.length(); ++stepIndex) {
                JSONObject stepObject = steps.getJSONObject(stepIndex);
                double stepSpeed =
                        Integer.parseInt(
                                stepObject.getJSONObject("distance").get("value").toString())
                        / (double)
                        Integer.parseInt(
                                stepObject.getJSONObject("duration").get("value").toString());
                for (LatLngPoint vertex
                        : decodePolyline(
                                stepObject.getJSONObject("polyline").get("points").toString())) {
                    points.add(new SimulationRoutePoint(vertex.lat, vertex.lng, stepSpeed));
                }
            }
        } catch (JSONException exception) {
            Log.e(TAG, "Exception", exception);
            return null;
        }
        return points;
    }

    /**
     * Decodes polyline binary data given by Google directions API. See
     * https://developers.google.com/maps/documentation/utilities/polylinealgorithm.
     */
    private static ArrayList<LatLngPoint> decodePolyline(String encoded) {
        ArrayList<LatLngPoint> poly = new ArrayList<LatLngPoint>();
        int index = 0;
        int len = encoded.length();
        int lat = 0;
        int lng = 0;
        while (index < len) {
            {
                int b;
                int shift = 0;
                int result = 0;
                do {
                    b = encoded.charAt(index++) - 63;
                    result |= (b & 0x1f) << shift;
                    shift += 5;
                } while (b >= 0x20);
                int dlat = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
                lat += dlat;
            }

            {
                int b;
                int shift = 0;
                int result = 0;
                do {
                    b = encoded.charAt(index++) - 63;
                    result |= (b & 0x1f) << shift;
                    shift += 5;
                } while (b >= 0x20);
                int dlng = ((result & 1) != 0 ? ~(result >> 1) : (result >> 1));
                lng += dlng;
            }

            poly.add(new LatLngPoint(lat / 1e5, lng / 1e5));
        }
        return poly;
    }
}

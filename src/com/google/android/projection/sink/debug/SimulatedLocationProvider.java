package com.google.android.projection.sink.debug;

import android.content.Context;
import android.location.Location;
import android.location.LocationListener;
import android.os.AsyncTask;
import android.os.Handler;
import android.util.Log;
import com.google.android.projection.sink.debug.simulation.LocationSimulator;
import com.google.android.projection.sink.debug.simulation.SimulationRoutePoint;
import com.google.android.projection.sink.debug.simulation.WebSimulationRouteProvider;
import com.google.android.projection.sink.settings.Settings;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * Provides control over simulated location. Represents a link between debug UI and real code.
 */
public class SimulatedLocationProvider {
    private static final String TAG = SimulatedLocationProvider.class.getSimpleName();
    private static final int ROUTE_SPEED_FACTOR_MAX = 1000;
    private static final int ROUTE_SPEED_FACTOR_MIN = 0;

    /**
     * UI update listener.
     */
    public interface UiListener {
        void onSpeedChanged(int speedMultiplier);
    }

    private final Handler mHandler = new Handler();
    private final Settings mSettings;
    private final Object mLocationSimulatorLock = new Object();
    private final LocationSimulator mLocationSimulator;
    private boolean mLocationSimulatorStarted;
    private final Set<LocationListener> mLocationClients = new HashSet<LocationListener>();
    private final Set<UiListener> mUiClients = new HashSet<UiListener>();

    public SimulatedLocationProvider(Context context) {
        mSettings = new Settings(context);
        mLocationSimulator = new LocationSimulator(mLocationSimulatorListener);
        mLocationSimulator.setRouteSpeedFactor(0);
    }

    public void shutdown() {
        synchronized (mLocationSimulatorLock) {
            mLocationSimulator.stopUpdates();
        }
    }

    public void registerUiListener(UiListener listener) {
        int speedFactor;
        synchronized (mLocationSimulatorLock) {
            speedFactor = mLocationSimulator.getRouteSpeedFactor();
        }
        listener.onSpeedChanged(speedFactor);
        mUiClients.add(listener);
    }

    public void unregisterUiListener(UiListener listener) {
        mUiClients.remove(listener);
    }

    public void requestUpdates(LocationListener listener) {
        if (!mLocationSimulatorStarted) {
            mLocationSimulatorStarted = true;

            final String origin = mSettings.getSimulationStart();
            final String destination = mSettings.getSimulationFinish();
            AsyncTask.execute(new Runnable() {
                @Override
                public void run() {
                    Log.d(TAG, String.format("Route from '%s' to '%s'.", origin, destination));
                    List<SimulationRoutePoint> points = WebSimulationRouteProvider.createPoints(
                            origin, destination, "driving");
                    if (points == null || points.isEmpty()) {
                        Log.w(TAG, "Can't get directions");
                    }
                    synchronized (mLocationSimulatorLock) {
                        mLocationSimulator.setRoute(points);
                        mLocationSimulator.startUpdates(1000);
                        mLocationSimulator.setRouteResumed(true);
                    }
                }
            });
        }
        mLocationClients.add(listener);
    }

    public void removeUpdates(LocationListener listener) {
        mLocationClients.remove(listener);
    }

    public void increaseSimulationSpeed() {
        int speedFactor;
        synchronized (mLocationSimulatorLock) {
            speedFactor = Math.min(mLocationSimulator.getRouteSpeedFactor() + 1,
                    ROUTE_SPEED_FACTOR_MAX);
            mLocationSimulator.setRouteSpeedFactor(speedFactor);
        }
        for (UiListener listener : mUiClients) {
            listener.onSpeedChanged(speedFactor);
        }
    }

    public void decreaseSimulationSpeed() {
        int speedFactor;
        synchronized (mLocationSimulatorLock) {
            speedFactor = Math.max(mLocationSimulator.getRouteSpeedFactor() - 1,
                    ROUTE_SPEED_FACTOR_MIN);
            mLocationSimulator.setRouteSpeedFactor(speedFactor);
        }
        for (UiListener listener : mUiClients) {
            listener.onSpeedChanged(speedFactor);
        }
    }

    public void resetSimulation() {
        synchronized (mLocationSimulatorLock) {
            mLocationSimulator.setRouteSpeedFactor(ROUTE_SPEED_FACTOR_MIN);
            mLocationSimulator.resetRoute();
        }
        for (UiListener listener : mUiClients) {
            listener.onSpeedChanged(ROUTE_SPEED_FACTOR_MIN);
        }
    }

    private final LocationSimulator.Listener mLocationSimulatorListener =
            new LocationSimulator.Listener() {

        @Override
        public void onSimulationUpdate(final Location location, double routeProgress) {
            if (location != null) {
                mHandler.post(new Runnable() {
                    @Override
                    public void run() {
                        for (LocationListener listener : mLocationClients) {
                            listener.onLocationChanged(location);
                        }
                    }
                });
            }
        }
    };
}

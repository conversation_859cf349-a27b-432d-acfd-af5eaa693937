<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    package="com.google.android.projection.sink">

  <uses-permission android:name="android.permission.BLUETOOTH_CONNECT" />
  <uses-permission android:name="android.permission.VIBRATE"/>
  <!-- Only used for GPS simulation route calculation. -->
  <uses-permission android:name="android.permission.INTERNET"/>
  <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION"/>
  <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION"/>
  <uses-permission android:name="android.permission.RECORD_AUDIO"/>
  <uses-permission android:name="android.permission.ACCESS_WIFI_STATE"/>
  <uses-permission android:name="android.permission.CHANGE_WIFI_STATE"/>
  <uses-permission android:name="android.permission.CHAN<PERSON>_NETWORK_STATE"/>
  <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE"/>
  <uses-permission android:name="android.permission.NFC"/>
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.BLUETOOTH"/>
  <uses-permission android:name="android.permission.BLUETOOTH_ADMIN"/>
  <!-- For bug report collection. -->
  <uses-permission android:name="android.permission.DUMP"/>
  <uses-permission android:name="android.permission.READ_LOGS"/>
  <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE"/>
  <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS"/>
  <uses-permission android:name="android.permission.ACCESS_NOTIFICATIONS"/>
  <uses-permission android:name="android.permission.SEND_RESPOND_VIA_MESSAGE"/>
  <uses-permission android:name="android.permission.CALL_PHONE"/>
  <uses-permission android:name="android.permission.CAMERA"/>
  <uses-permission android:name="android.permission.RECORD_VIDEO"/>
  <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE"/>

  <application
      android:allowBackup="false"
      android:label="@string/app_name"
      android:theme="@android:style/Theme.Holo.Light.NoActionBar"
      android:icon="@drawable/ic_launcher">
    <activity
        android:name="com.google.android.projection.sink.ui.MainActivity"
        android:screenOrientation="sensorLandscape"
        android:configChanges="orientation|screenSize|keyboardHidden|smallestScreenSize|screenLayout|uiMode|locale|fontScale"
        android:windowSoftInputMode="stateHidden"
        android:icon="@drawable/ic_launcher"
        android:launchMode="singleTask"
        android:label="@string/app_name"
        android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN"/>
        <category android:name="android.intent.category.LAUNCHER"/>
      </intent-filter>
      <intent-filter>
        <action android:name="android.hardware.usb.action.USB_ACCESSORY_ATTACHED"/>
      </intent-filter>
      <intent-filter>
        <action android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"/>
      </intent-filter>

      <meta-data
          android:name="android.hardware.usb.action.USB_DEVICE_ATTACHED"
          android:resource="@xml/usb_device_filter"/>
      <meta-data
          android:name="android.hardware.usb.action.USB_ACCESSORY_ATTACHED"
          android:resource="@xml/accessory_filter"/>
    </activity>
    <activity
        android:name="com.google.android.projection.sink.settings.SettingsActivity"
        android:icon="@drawable/ic_launcher_settings"
        android:label="@string/title_activity_settings"
        android:exported="true">
      <intent-filter>
        <action android:name="android.intent.action.MAIN"/>
        <category android:name="android.intent.category.LAUNCHER"/>
      </intent-filter>
    </activity>
    <activity
        android:name="com.google.android.projection.sink.settings.SimulationSettingsActivity"
        android:label="@string/title_activity_simulation_settings"
        android:parentActivityName="com.google.android.projection.sink.settings.SettingsActivity">
      <meta-data
          android:name="android.support.PARENT_ACTIVITY"
          android:value="com.google.android.projection.sink.settings.SettingsActivity"/>
    </activity>

    <receiver android:name=".settings.SettingsBroadcastReceiver"
        android:exported="true">
      <intent-filter>
        <action android:name="com.google.android.projection.sink.SETTINGS"/>
      </intent-filter>
    </receiver>
  </application>
</manifest>
